
import React, { useState } from "react";
import { Navigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Activity, Loader2 } from "lucide-react";

const Login = () => {
  const { user, login, loading } = useAuth();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState("");

  // مسح قيم حقول الإدخال عند تحميل الصفحة (معطل مؤقتاً للاختبار)
  React.useEffect(() => {
    console.log('🔄 تحميل صفحة تسجيل الدخول');
    // مسح الأخطاء فقط
    setError("");

    // لا نمسح قيم الحقول للاختبار
    // setEmail("");
    // setPassword("");
  }, []);

  // إذا كان التطبيق في حالة تحميل، نعرض شاشة تحميل
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Loader2 className="h-8 w-8 animate-spin text-primary-600" />
      </div>
    );
  }

  // إذا كان المستخدم مسجل الدخول بالفعل، ننتقل إلى لوحة التحكم
  if (user) {
    return <Navigate to="/dashboard" />;
  }

  const handleLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError("");
    setIsSubmitting(true);

    console.log('🔍 بيانات النموذج:', {
      email,
      password,
      emailLength: email.length,
      passwordLength: password.length,
      emailValue: email,
      passwordValue: password
    });

    // التحقق من وجود البيانات
    if (!email || !password) {
      setError("يرجى إدخال البريد الإلكتروني وكلمة المرور");
      setIsSubmitting(false);
      return;
    }

    try {
      await login(email, password);
      // Successful login will redirect via the conditional above
    } catch (err) {
      setError("خطأ في تسجيل الدخول. يرجى التحقق من الاسم أو البريد الإلكتروني وكلمة المرور");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-md">
        <div className="text-center mb-8">
          <div className="flex justify-center">
            <Activity className="h-12 w-12 text-primary-600" />
          </div>
          <h1 className="mt-4 text-3xl font-bold text-gray-900">نظام الأفلييت</h1>
          <p className="mt-2 text-gray-600">تسجيل الدخول للوصول إلى لوحة التحكم</p>
        </div>

        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="text-xl font-bold text-center">تسجيل الدخول</CardTitle>
            <CardDescription className="text-center">أدخل بياناتك للوصول إلى حسابك</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleLogin} autoComplete="off" autoCorrect="off" spellCheck="false" data-form-type="other">
              <div className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="email" className="block text-sm font-medium text-right">
                    اسم المستخدم أو البريد الإلكتروني
                  </label>
                  <Input
                    id="email"
                    name={`email_${Date.now()}`}
                    type="text"
                    placeholder="أدخل الاسم أو البريد الإلكتروني"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    required
                    className="w-full text-right"
                    autoComplete="new-password"
                    autoCorrect="off"
                    autoCapitalize="off"
                    spellCheck="false"
                    data-form-type="other"
                    autoFocus
                  />
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <a onClick={(e) => e.preventDefault()} className="text-sm text-primary-600 hover:text-primary-500 cursor-pointer">
                      نسيت كلمة المرور؟
                    </a>
                    <label htmlFor="password" className="block text-sm font-medium">
                      كلمة المرور
                    </label>
                  </div>
                  <Input
                    id="password"
                    name={`password_${Date.now()}`}
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    required
                    className="w-full"
                    autoComplete="new-password"
                    autoCorrect="off"
                    autoCapitalize="off"
                    spellCheck="false"
                    data-form-type="other"
                  />
                </div>

                {error && (
                  <div className="bg-red-50 text-red-700 p-3 rounded-md text-center text-sm">
                    {error}
                  </div>
                )}

                <Button
                  type="submit"
                  className="w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <Loader2 className="h-4 w-4 animate-spin ml-2" />
                  ) : null}
                  تسجيل الدخول
                </Button>
              </div>
            </form>
          </CardContent>
          <CardFooter>
            <div className="w-full text-center text-sm text-gray-500">
              نظام إدارة الأفلييت - جميع الحقوق محفوظة &copy; {new Date().getFullYear()}
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default Login;
