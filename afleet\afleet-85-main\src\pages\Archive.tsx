import DashboardLayout from "@/components/layout/DashboardLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Order, OrderStatus } from "./Orders";
import { getOrdersBySection, updateOrderStatus, updateOrder, invalidateAllOrderQueries } from "@/services/localStorageService";
import { Search, Archive, RefreshCcw, AlertTriangle, Info } from "lucide-react";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

const ArchivePage = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isReactivateDialogOpen, setIsReactivateDialogOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [sortField, setSortField] = useState<"updatedAt" | "orderNumber" | "customerName">("updatedAt");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  // عدد الطلبات في الصفحة الواحدة
  const ordersPerPage = 50;

  // جلب الطلبات من الأرشيف
  const { data: orders = [], isLoading } = useQuery({
    queryKey: ["archive-orders"],
    queryFn: () => getOrdersBySection("archive"),
  });

  // تصفية الطلبات حسب البحث
  const filteredOrders = orders.filter((order) => {
    if (!searchTerm.trim()) return true; // إذا كان البحث فارغًا، أظهر جميع الطلبات

    const searchLower = searchTerm.toLowerCase().trim();

    // البحث في رقم الطلب
    if (order.orderNumber.toLowerCase().includes(searchLower)) return true;

    // البحث في اسم العميل
    if (order.customerName && order.customerName.toLowerCase().includes(searchLower)) return true;

    // البحث في رقم هاتف العميل
    if (order.customerPhone && order.customerPhone.includes(searchTerm.trim())) return true;

    // البحث في رقم هاتف العميل البديل
    if (order.customerPhone2 && order.customerPhone2.includes(searchTerm.trim())) return true;

    // البحث في اسم المسوق
    if (order.marketerName && order.marketerName.toLowerCase().includes(searchLower)) return true;

    // البحث في عنوان العميل
    if (order.customerAddress && order.customerAddress.toLowerCase().includes(searchLower)) return true;

    // البحث في سبب الإلغاء
    if (order.cancellationReason && order.cancellationReason.toLowerCase().includes(searchLower)) return true;

    return false;
  });

  // ترتيب الطلبات
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    let comparison = 0;

    switch (sortField) {
      case "updatedAt":
        // ترتيب حسب تاريخ التحديث (الأحدث أولاً)
        comparison = new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        break;
      case "orderNumber":
        // ترتيب حسب رقم الطلب
        comparison = a.orderNumber.localeCompare(b.orderNumber);
        break;
      case "customerName":
        // ترتيب حسب اسم العميل
        comparison = (a.customerName || "").localeCompare(b.customerName || "");
        break;
      default:
        // الترتيب الافتراضي حسب تاريخ التحديث (الأحدث أولاً)
        comparison = new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
    }

    // تطبيق اتجاه الترتيب
    if (sortField === "updatedAt") {
      // للتاريخ، نريد دائمًا الأحدث في الأعلى بغض النظر عن اتجاه الترتيب
      return sortDirection === "asc" ? -comparison : comparison;
    } else {
      // لباقي الحقول، نتبع اتجاه الترتيب المحدد
      return sortDirection === "asc" ? comparison : -comparison;
    }
  });

  // حساب إجمالي عدد الصفحات
  const totalPages = Math.ceil(sortedOrders.length / ordersPerPage);

  // الحصول على الطلبات للصفحة الحالية
  const startIndex = (currentPage - 1) * ordersPerPage;
  const endIndex = startIndex + ordersPerPage;
  const paginatedOrders = sortedOrders.slice(startIndex, endIndex);

  // تغيير الصفحة
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // تغيير حقل الترتيب
  const handleSortChange = (field: "updatedAt" | "orderNumber" | "customerName") => {
    if (field === sortField) {
      // إذا كان نفس الحقل، قم بتبديل اتجاه الترتيب
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // إذا كان حقل مختلف، قم بتعيين الحقل الجديد واتجاه الترتيب الافتراضي
      setSortField(field);
      setSortDirection("desc");
    }
  };

  // فتح نافذة إعادة تنشيط الطلب
  const openReactivateDialog = (order: Order) => {
    setSelectedOrder(order);
    setIsReactivateDialogOpen(true);
  };

  // إعادة تنشيط الطلب
  const handleReactivateOrder = async () => {
    if (!selectedOrder) return;

    try {
      setIsUpdating(true);

      // تحديث حالة الطلب إلى "قيد الانتظار"
      const updatedOrder = {
        ...selectedOrder,
        status: "pending" as OrderStatus,
        section: "orders" as OrderSection, // تحديث القسم إلى "orders"
        updatedAt: new Date().toISOString()
      };

      // تحديث الطلب
      await updateOrderStatus(selectedOrder.id, "pending");
      await updateOrder(updatedOrder);

      // تحديث جميع الاستعلامات ذات الصلة
      invalidateAllOrderQueries(queryClient);

      toast.success("تم إعادة تنشيط الطلب بنجاح");
      setIsReactivateDialogOpen(false);
    } catch (error) {
      toast.error("حدث خطأ أثناء إعادة تنشيط الطلب");
      console.error(error);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="flex flex-col gap-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">أرشيف الطلبات</h1>
            <p className="text-muted-foreground">
              إدارة الطلبات المكتملة والملغاة والمحصلة
            </p>
          </div>
        </div>

        <div className="flex justify-between items-center gap-4">
          <div className="flex items-center">
            {searchTerm.trim() && (
              <div className="text-sm text-muted-foreground">
                نتائج البحث: <span className="font-medium text-primary">{filteredOrders.length}</span> طلب
                {filteredOrders.length > 0 && searchTerm.trim() && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 px-2 text-xs mr-2"
                    onClick={() => setSearchTerm("")}
                  >
                    مسح البحث
                  </Button>
                )}
              </div>
            )}
          </div>
          <div className="relative w-64">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="بحث عن طلب، عميل، رقم هاتف..."
              className="pl-10 h-9 rounded-full bg-gray-50 border-gray-200 focus:border-primary focus:ring-1 focus:ring-primary"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>الطلبات المؤرشفة</CardTitle>
            <CardDescription>
              {filteredOrders.length} طلب (مكتمل، ملغي، محصل)
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              </div>
            ) : filteredOrders.length === 0 ? (
              <div className="text-center py-10">
                <Archive className="h-10 w-10 text-muted-foreground mx-auto mb-2" />
                <h3 className="text-lg font-medium">لا توجد طلبات</h3>
                <p className="text-muted-foreground">
                  لا توجد طلبات في الأرشيف حاليًا
                </p>
              </div>
            ) : (
              <div className="overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-24 text-center">
                        <button
                          className="flex flex-col items-center w-full hover:bg-gray-50 py-1 rounded-md transition-colors"
                          onClick={() => handleSortChange("orderNumber")}
                        >
                          <div className="flex items-center gap-1">
                            <span>رقم</span>
                          </div>
                          <span>الطلب</span>
                        </button>
                      </TableHead>
                      <TableHead className="w-64 text-center">
                        <button
                          className="flex flex-col items-center w-full hover:bg-gray-50 py-1 rounded-md transition-colors"
                          onClick={() => handleSortChange("customerName")}
                        >
                          <div className="flex items-center gap-1">
                            <span>بيانات</span>
                          </div>
                          <span>العميل</span>
                        </button>
                      </TableHead>
                      <TableHead className="w-28 text-center">
                        <div className="flex flex-col items-center">
                          <span>المحافظة</span>
                        </div>
                      </TableHead>
                      <TableHead className="w-32 text-center">
                        <div className="flex flex-col items-center">
                          <span>حالة</span>
                          <span>الطلب</span>
                        </div>
                      </TableHead>
                      <TableHead className="w-32 text-center">
                        <div className="flex flex-col items-center">
                          <span>ملاحظات</span>
                        </div>
                      </TableHead>
                      <TableHead className="w-32 text-center">
                        <button
                          className="flex flex-col items-center w-full hover:bg-gray-50 py-1 rounded-md transition-colors"
                          onClick={() => handleSortChange("updatedAt")}
                        >
                          <div className="flex items-center gap-1">
                            <span>تاريخ</span>
                          </div>
                          <span>التحديث</span>
                        </button>
                      </TableHead>
                      <TableHead className="w-28 text-center">
                        <div className="flex flex-col items-center">
                          <span>الإجمالي</span>
                        </div>
                      </TableHead>
                      <TableHead className="w-auto text-center">
                        <div className="flex flex-col items-center">
                          <span>الإجراءات</span>
                        </div>
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {paginatedOrders.map((order) => (
                      <TableRow key={order.id}>
                        <TableCell
                          className="font-medium cursor-pointer hover:text-primary"
                          onClick={() => navigate(`/order-details/${order.id}`)}
                        >
                          #{order.orderNumber}
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            <div className="font-bold text-primary-700 text-base mb-1 border-b pb-1">
                              {order.customerName || "بدون اسم"}
                            </div>
                            <div className="flex items-center gap-1 mt-1">
                              <span className="text-gray-500">📱</span>
                              <span className="text-sm">{order.customerPhone}</span>
                            </div>
                            {order.customerAddress && (
                              <div className="flex items-start gap-1 mt-1">
                                <span className="text-gray-500 mt-0.5">📍</span>
                                <span className="text-xs text-muted-foreground truncate max-w-[180px]" title={order.customerAddress}>
                                  {order.customerAddress}
                                </span>
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{order.province}</TableCell>
                        <TableCell>
                          <Badge className={
                            order.status === "delivered" ? "bg-green-100 text-green-800" :
                            order.status === "partially_delivered" ? "bg-yellow-100 text-yellow-800" :
                            order.status === "delivery_rejected" ? "bg-red-100 text-red-800" :
                            order.status === "cancelled" ? "bg-gray-100 text-gray-800" :
                            "bg-blue-100 text-blue-800"
                          }>
                            {order.status === "delivered" && "تم التسليم"}
                            {order.status === "partially_delivered" && "تسليم جزئي"}
                            {order.status === "delivery_rejected" && "تم رفض الاستلام"}
                            {order.status === "cancelled" && "ملغي"}
                            {!["delivered", "partially_delivered", "delivery_rejected", "cancelled"].includes(order.status) && order.status}
                          </Badge>
                          {order.shippingCompany && (
                            <div className="text-xs text-muted-foreground mt-1">
                              شركة الشحن: {order.shippingCompany}
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="text-sm text-muted-foreground truncate max-w-[150px]" title={order.collectionNotes || order.cancellationReason || "غير محدد"}>
                            {order.collectionNotes || order.cancellationReason || "غير محدد"}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">
                            {new Date(order.collectionDate || order.cancellationDate || order.updatedAt).toLocaleDateString("ar-EG")}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {new Date(order.collectionDate || order.cancellationDate || order.updatedAt).toLocaleTimeString("ar-EG", {
                              hour: "2-digit",
                              minute: "2-digit"
                            })}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium text-gray-700">
                            {order.totalAmount} ج.م
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex gap-2">
                            <Button
                              variant="default"
                              size="sm"
                              className="bg-blue-600 hover:bg-blue-700"
                              onClick={() => openReactivateDialog(order)}
                            >
                              <RefreshCcw className="h-4 w-4 mr-1" />
                              إعادة تنشيط
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => navigate(`/order-details/${order.id}`)}
                            >
                              <Info className="h-4 w-4 mr-1" />
                              التفاصيل
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>

                {/* نظام الصفحات */}
                {totalPages > 1 && (
                  <div className="flex justify-center mt-6">
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious
                            onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                            className={currentPage === 1 ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
                          />
                        </PaginationItem>

                        {/* عرض أرقام الصفحات */}
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          // حساب أرقام الصفحات التي سيتم عرضها
                          let pageNum;
                          if (totalPages <= 5) {
                            // إذا كان إجمالي الصفحات 5 أو أقل، اعرض جميع الصفحات
                            pageNum = i + 1;
                          } else if (currentPage <= 3) {
                            // إذا كانت الصفحة الحالية في البداية
                            if (i < 4) {
                              pageNum = i + 1;
                            } else {
                              return (
                                <PaginationItem key="ellipsis-end">
                                  <PaginationEllipsis />
                                </PaginationItem>
                              );
                            }
                          } else if (currentPage >= totalPages - 2) {
                            // إذا كانت الصفحة الحالية في النهاية
                            if (i === 0) {
                              return (
                                <PaginationItem key="ellipsis-start">
                                  <PaginationEllipsis />
                                </PaginationItem>
                              );
                            } else {
                              pageNum = totalPages - (4 - i);
                            }
                          } else {
                            // إذا كانت الصفحة الحالية في المنتصف
                            if (i === 0) {
                              return (
                                <PaginationItem key="ellipsis-start">
                                  <PaginationEllipsis />
                                </PaginationItem>
                              );
                            } else if (i === 4) {
                              return (
                                <PaginationItem key="ellipsis-end">
                                  <PaginationEllipsis />
                                </PaginationItem>
                              );
                            } else {
                              pageNum = currentPage + (i - 2);
                            }
                          }

                          return pageNum ? (
                            <PaginationItem key={pageNum}>
                              <PaginationLink
                                onClick={() => handlePageChange(pageNum)}
                                isActive={currentPage === pageNum}
                              >
                                {pageNum}
                              </PaginationLink>
                            </PaginationItem>
                          ) : null;
                        })}

                        <PaginationItem>
                          <PaginationNext
                            onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                            className={currentPage === totalPages ? "opacity-50 cursor-not-allowed" : "cursor-pointer"}
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                )}

                {/* عرض معلومات الصفحات */}
                <div className="text-center text-sm text-muted-foreground mt-2">
                  عرض {startIndex + 1} إلى {Math.min(endIndex, filteredOrders.length)} من إجمالي {filteredOrders.length} طلب
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* نافذة إعادة تنشيط الطلب */}
      <Dialog open={isReactivateDialogOpen} onOpenChange={setIsReactivateDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>إعادة تنشيط الطلب</DialogTitle>
            <DialogDescription>
              هل أنت متأكد من إعادة تنشيط الطلب #{selectedOrder?.orderNumber}؟
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="bg-blue-50 border border-blue-100 rounded-md p-4">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-blue-600 mt-0.5 ml-2" />
                <div>
                  <h4 className="font-medium text-blue-800">ملاحظة</h4>
                  <p className="text-sm text-blue-700 mt-1">
                    عند إعادة تنشيط الطلب، سيتم تحويله إلى حالة "قيد الانتظار" ونقله إلى قسم الطلبات.
                  </p>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsReactivateDialogOpen(false)}>
              إلغاء
            </Button>
            <Button
              onClick={handleReactivateOrder}
              disabled={isUpdating}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isUpdating ? "جاري التنفيذ..." : "تأكيد إعادة التنشيط"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
};

export default ArchivePage;
