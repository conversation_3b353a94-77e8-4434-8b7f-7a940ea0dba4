import { Employee } from "@/types/employeeTypes";
import employeesData from "@/data/employees.json";

// بيانات الموظفين من الملف الثابت
const staticEmployees: Employee[] = employeesData as Employee[];

// مفاتيح التخزين المحلي (للجلسة الحالية فقط)
const STORAGE_KEYS = {
  CURRENT_EMPLOYEE: "current_employee",
  IS_AUTHENTICATED: "isAuthenticated",
  USER_ROLE: "userRole",
};

// تهيئة بيانات الموظفين (للتوافق مع الكود الحالي)
export const initializeEmployees = (): void => {
  console.log('🔄 تهيئة بيانات الموظفين من الملف الثابت...');
  console.log('👥 الموظفين المتاحين للدخول:');
  
  staticEmployees.forEach(emp => {
    console.log(`👤 الاسم: ${emp.name} | 🔑 كلمة المرور: ${emp.password} | 📧 الإيميل: ${emp.email} | ✅ ${emp.isActive ? 'نشط' : 'غير نشط'}`);
  });
  
  console.log('💡 يمكن تسجيل الدخول بالاسم أو البريد الإلكتروني + كلمة المرور');
  
  // إضافة دوال للتطوير
  (window as any).showEmployees = () => {
    console.log('👥 عرض جميع الموظفين:');
    console.table(staticEmployees);
    return staticEmployees;
  };
  
  (window as any).testLogin = (identifier: string, password: string) => {
    console.log('🧪 اختبار تسجيل الدخول...');
    const result = loginEmployee(identifier, password);
    console.log('🧪 النتيجة:', result ? `نجح - ${result.name}` : 'فشل');
    return result;
  };
  
  (window as any).forceLogin = () => {
    console.log('🔧 فرض تسجيل دخول الأدمن...');
    const admin = staticEmployees[0]; // أول موظف (الأدمن)
    setCurrentEmployee(admin);
    console.log('✅ تم فرض تسجيل دخول الأدمن');
    return admin;
  };
};

// الحصول على جميع الموظفين
export const getEmployees = (): Employee[] => {
  return staticEmployees;
};

// الحصول على موظف بواسطة المعرف
export const getEmployeeById = (id: string): Employee | undefined => {
  return staticEmployees.find((employee) => employee.id === id);
};

// الحصول على موظف بواسطة البريد الإلكتروني
export const getEmployeeByEmail = (email: string): Employee | undefined => {
  return staticEmployees.find((employee) => employee.email === email);
};

// الحصول على موظف بواسطة الاسم
export const getEmployeeByName = (name: string): Employee | undefined => {
  return staticEmployees.find((employee) => employee.name.toLowerCase() === name.toLowerCase());
};

// الحصول على موظف بواسطة الاسم أو الإيميل
export const getEmployeeByNameOrEmail = (identifier: string): Employee | undefined => {
  const lowerIdentifier = identifier.toLowerCase().trim();
  
  console.log('🔍 البحث عن:', lowerIdentifier);
  
  // البحث بالإيميل أولاً
  let employee = staticEmployees.find((emp) => emp.email.toLowerCase() === lowerIdentifier);
  console.log('🔍 البحث بالإيميل:', employee ? 'موجود' : 'غير موجود');
  
  // إذا لم يوجد، ابحث بالاسم
  if (!employee) {
    employee = staticEmployees.find((emp) => emp.name.toLowerCase() === lowerIdentifier);
    console.log('🔍 البحث بالاسم:', employee ? 'موجود' : 'غير موجود');
  }
  
  return employee;
};

// تسجيل دخول موظف (بالاسم أو الإيميل)
export const loginEmployee = (identifier: string, password: string): Employee | null => {
  console.log('🔍 البحث عن موظف بالاسم أو الإيميل:', identifier);
  
  const employee = getEmployeeByNameOrEmail(identifier);
  console.log('🔍 الموظف الموجود:', employee ? { name: employee.name, email: employee.email, isActive: employee.isActive } : 'غير موجود');
  
  if (!employee) {
    console.error('❌ لم يتم العثور على موظف بهذا الاسم أو الإيميل');
    return null;
  }
  
  if (employee.password !== password) {
    console.error('❌ كلمة المرور غير صحيحة');
    console.log('🔍 كلمة المرور المحفوظة:', employee.password);
    console.log('🔍 كلمة المرور المدخلة:', password);
    return null;
  }
  
  if (!employee.isActive) {
    console.error('❌ الحساب غير نشط');
    throw new Error("الحساب غير نشط");
  }
  
  console.log('✅ تسجيل دخول ناجح للموظف:', employee.name);
  
  // حفظ الموظف الحالي في التخزين المحلي (للجلسة فقط)
  setCurrentEmployee(employee);
  
  // تعيين حالة المصادقة
  try {
    localStorage.setItem(STORAGE_KEYS.IS_AUTHENTICATED, 'true');
    localStorage.setItem(STORAGE_KEYS.USER_ROLE, employee.role);
  } catch (error) {
    console.warn('⚠️ تعذر حفظ حالة المصادقة في localStorage');
  }
  
  return employee;
};

// تسجيل خروج الموظف الحالي
export const logoutEmployee = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEYS.CURRENT_EMPLOYEE);
    localStorage.removeItem(STORAGE_KEYS.IS_AUTHENTICATED);
    localStorage.removeItem(STORAGE_KEYS.USER_ROLE);
  } catch (error) {
    console.warn('⚠️ تعذر مسح بيانات الجلسة من localStorage');
  }
};

// الحصول على الموظف الحالي
export const getCurrentEmployee = (): Employee | null => {
  try {
    const storedEmployee = localStorage.getItem(STORAGE_KEYS.CURRENT_EMPLOYEE);
    if (storedEmployee) {
      return JSON.parse(storedEmployee);
    }
  } catch (error) {
    console.warn('⚠️ تعذر قراءة بيانات الموظف الحالي من localStorage');
  }
  return null;
};

// تعيين الموظف الحالي
export const setCurrentEmployee = (employee: Employee): void => {
  try {
    localStorage.setItem(STORAGE_KEYS.CURRENT_EMPLOYEE, JSON.stringify(employee));
  } catch (error) {
    console.warn('⚠️ تعذر حفظ بيانات الموظف الحالي في localStorage');
    // يمكن استخدام متغير عام كبديل
    (window as any).currentEmployee = employee;
  }
};

// التحقق من صلاحيات الموظف
export const hasPermission = (
  employee: Employee | null,
  section: string,
  action: string = "view"
): boolean => {
  if (!employee) {
    return false;
  }
  
  // المدير لديه جميع الصلاحيات
  if (employee.role === "admin") {
    return true;
  }
  
  // التحقق من الصلاحيات المحددة
  if (employee.permissions) {
    const sectionPermission = employee.permissions.find(p => p.section === section);
    if (sectionPermission) {
      return sectionPermission.actions.includes(action);
    }
  }
  
  // التحقق من الأقسام المتاحة (للتوافق مع النظام القديم)
  if (employee.accessibleSections) {
    return employee.accessibleSections.includes(section);
  }
  
  return false;
};

// التحقق من حالة المصادقة
export const isAuthenticated = (): boolean => {
  try {
    const authStatus = localStorage.getItem(STORAGE_KEYS.IS_AUTHENTICATED);
    const currentEmployee = getCurrentEmployee();
    return authStatus === 'true' && currentEmployee !== null;
  } catch (error) {
    console.warn('⚠️ تعذر التحقق من حالة المصادقة');
    // التحقق من المتغير العام كبديل
    return (window as any).currentEmployee !== undefined;
  }
};

// دوال للتوافق مع الكود الحالي (لا تفعل شيئاً لأن البيانات ثابتة)
export const addEmployee = (employee: Omit<Employee, "id" | "createdAt">): Employee => {
  throw new Error("لا يمكن إضافة موظفين جدد في النسخة الثابتة");
};

export const updateEmployee = (id: string, data: Partial<Employee>): Employee | null => {
  console.warn('⚠️ لا يمكن تحديث بيانات الموظفين في النسخة الثابتة');
  return getEmployeeById(id) || null;
};

export const deleteEmployee = (id: string): boolean => {
  throw new Error("لا يمكن حذف الموظفين في النسخة الثابتة");
};

export const canDeleteEmployee = (id: string): { canDelete: boolean; reason?: string } => {
  return { canDelete: false, reason: 'لا يمكن حذف الموظفين في النسخة الثابتة' };
};

// إعادة تعيين البيانات (للتوافق)
export const resetEmployeesData = (): void => {
  console.log('🔄 إعادة تعيين البيانات (النسخة الثابتة)');
  logoutEmployee();
};

export const clearAllData = (): void => {
  console.log('🗑️ مسح البيانات (النسخة الثابتة)');
  logoutEmployee();
};

export const forceInitializeEmployees = (): void => {
  console.log('🔧 فرض تهيئة البيانات (النسخة الثابتة)');
  initializeEmployees();
};
