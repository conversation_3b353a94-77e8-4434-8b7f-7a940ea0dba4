// نموذج الفاتورة المشترك للمشروع
export interface InvoiceData {
  orderNumber: string;
  customerName: string;
  customerPhone: string;
  customerAddress: string;
  notes?: string;
  items: Array<{
    id: string;
    name: string;
    quantity: number;
    price: number;
    total: number;
  }>;
  subtotal: number;
  shippingFee: number;
  total: number;
  date: string;
}

export interface CompanySettings {
  companyName: string;
  companyLogo?: string;
  companyPhone: string;
  companyEmail: string;
  companyAddress: string;
}

export const generateInvoiceHTML = (invoiceData: InvoiceData, companySettings: CompanySettings): string => {
  return `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <title>فاتورة #${invoiceData.orderNumber}</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Scheherazade+New:wght@400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@100;200;300;400;500;600;700&display=swap');
        body {
          font-family: 'Tajawal', 'Noto Sans Arabic', 'IBM Plex Sans Arabic', 'Amiri', 'Scheherazade New', 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          margin: 0;
          padding: 10px;
          direction: rtl;
          background: #ffffff;
          color: #000000 !important;
          font-size: 14px;
          font-weight: 700;
        }
        * {
          color: #000000 !important;
        }
        .invoice-container {
          max-width: 800px;
          margin: 0 auto;
          background: white;
          border-radius: 12px;
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
          overflow: hidden;
          position: relative;
          min-height: auto;
          page-break-inside: avoid;
        }
        .header {
          background: #ffffff;
          color: #000000 !important;
          padding: 20px;
          position: relative;
          border-bottom: 2px solid #000000;
        }
        .header * {
          color: #000000 !important;
        }
        .header-content {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          position: relative;
          z-index: 2;
        }
        .company-section {
          text-align: center;
          flex: 1;
        }
        .company-name {
          font-family: 'Tajawal', 'Noto Sans Arabic', 'IBM Plex Sans Arabic', 'Amiri', sans-serif;
          font-size: 26px;
          font-weight: 900;
          margin-bottom: 5px;
          color: #000000 !important;
          text-shadow: none;
          letter-spacing: 0.5px;
        }
        .company-logo {
          position: absolute;
          top: 15px;
          right: 20px;
          height: 60px;
          width: 60px;
          background: white;
          border-radius: 8px;
          padding: 5px;
          box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }
        .company-logo img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
        .invoice-info {
          position: absolute;
          top: 15px;
          left: 20px;
          text-align: left;
          background: #ffffff;
          padding: 10px;
          border-radius: 6px;
          border: 1px solid #cccccc;
        }
        .invoice-number {
          font-family: 'Tajawal', 'Noto Sans Arabic', 'IBM Plex Sans Arabic', sans-serif;
          font-size: 20px;
          font-weight: 900;
          margin-bottom: 5px;
          color: #000000 !important;
          text-shadow: none;
          letter-spacing: 0.3px;
        }
        .invoice-date {
          font-size: 14px;
          font-weight: 700;
          color: #000000 !important;
          text-shadow: none;
        }

        .content {
          padding: 15px;
        }
        .section {
          margin-bottom: 12px;
        }
        .section-title {
          font-size: 16px;
          font-weight: bold;
          color: #000000;
          margin-bottom: 8px;
          padding-bottom: 4px;
          border-bottom: 2px solid #000000;
        }
        .customer-info {
          background: #f5f5f5;
          padding: 12px;
          border-radius: 6px;
          border-left: 3px solid #000000;
        }
        .customer-row {
          display: flex;
          margin-bottom: 8px;
        }
        .customer-label {
          font-weight: bold;
          width: 80px;
          color: #000000;
        }
        .notes-section {
          background: #ffffff;
          padding: 8px;
          border: 2px solid #000000;
          margin-top: 8px;
        }
        .notes-title {
          font-weight: bold;
          color: #000000;
          margin-bottom: 4px;
          font-size: 13px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 15px 0;
          border: 2px solid #000000;
        }
        th {
          background: #ffffff !important;
          color: #000000 !important;
          padding: 12px 8px;
          text-align: center;
          font-weight: 900;
          font-size: 14px;
          border: 2px solid #000000;
          border-bottom: 3px solid #000000;
        }
        thead th {
          background: #ffffff !important;
          color: #000000 !important;
          font-weight: 900;
        }
        td {
          padding: 10px 8px;
          text-align: center;
          border: 1px solid #000000;
          background: #ffffff;
          font-size: 13px;
          color: #000000 !important;
          font-weight: 600;
        }
        .summary {
          margin-top: 15px;
          display: flex;
          justify-content: flex-end;
        }
        .summary-box {
          background: #f5f5f5;
          border: 1px solid #cccccc;
          border-radius: 6px;
          padding: 12px;
          width: 250px;
        }
        .summary-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 4px;
          padding: 2px 0;
          font-size: 13px;
          color: #000000;
        }
        .summary-total {
          font-weight: bold;
          font-size: 14px;
          border-top: 2px solid #000000;
          padding-top: 6px;
          margin-top: 6px;
          color: #000000;
        }
        .footer {
          background: #ffffff;
          color: #000000 !important;
          padding: 15px;
          text-align: center;
          border-top: 2px solid #000000;
        }
        .footer * {
          color: #000000 !important;
        }
        .footer-message {
          background: #ffffff;
          color: #000000 !important;
          padding: 10px;
          margin: 10px 0;
          border: 2px solid #000000;
          font-weight: bold;
          text-align: center;
          font-size: 14px;
          page-break-inside: avoid;
        }
        .footer {
          page-break-inside: avoid;
          break-inside: avoid;
        }
        .invoice-container {
          page-break-inside: avoid;
        }
        @media print {
          .invoice-container {
            page-break-inside: avoid;
            break-inside: avoid;
          }
          .footer-message, .footer {
            page-break-before: avoid;
            page-break-inside: avoid;
          }
        }
      </style>
    </head>
    <body>
      <div class="invoice-container">

        <!-- Header Section -->
        <div class="header">
          <!-- Logo (Right) -->
          ${companySettings.companyLogo ? `
          <div class="company-logo">
            <img src="${companySettings.companyLogo}" alt="شعار الشركة" />
          </div>
          ` : ''}
          
          <!-- Company Name (Center) -->
          <div class="company-section">
            <div class="company-name">${companySettings.companyName}</div>
            <div style="font-size: 14px; opacity: 0.9;">فاتورة مبيعات</div>
          </div>
          
          <!-- Invoice Info (Left) -->
          <div class="invoice-info">
            <div class="invoice-number">فاتورة #${invoiceData.orderNumber}</div>
            <div class="invoice-date">${invoiceData.date}</div>
          </div>
        </div>

        <!-- Content Section -->
        <div class="content">
          <!-- Customer Information -->
          <div class="section">
            <div class="section-title">📋 بيانات العميل</div>
            <div class="customer-info">
              <div class="customer-row">
                <span class="customer-label">الاسم:</span>
                <span>${invoiceData.customerName}</span>
              </div>
              <div class="customer-row">
                <span class="customer-label">الهاتف:</span>
                <span>${invoiceData.customerPhone}</span>
              </div>
              <div class="customer-row">
                <span class="customer-label">العنوان:</span>
                <span>${invoiceData.customerAddress}</span>
              </div>
              
              ${invoiceData.notes ? `
              <!-- Notes Section -->
              <div class="notes-section">
                <div class="notes-title">📝 ملاحظات:</div>
                <div>${invoiceData.notes}</div>
              </div>
              ` : ''}
            </div>
          </div>

          <!-- Products Section -->
          <div class="section">
            <div class="section-title">🛍️ تفاصيل المنتجات</div>
            <table>
              <thead>
                <tr>
                  <th>#</th>
                  <th>اسم المنتج</th>
                  <th>الكمية</th>
                  <th>السعر</th>
                  <th>الإجمالي</th>
                </tr>
              </thead>
              <tbody>
                ${invoiceData.items.map((item, index) => `
                <tr>
                  <td>${index + 1}</td>
                  <td>${item.name}</td>
                  <td>${item.quantity}</td>
                  <td>${item.price.toLocaleString()} ج.م</td>
                  <td>${item.total.toLocaleString()} ج.م</td>
                </tr>
                `).join('')}
              </tbody>
            </table>
          </div>

          <!-- Summary Section -->
          <div class="summary">
            <div class="summary-box">
              <div class="summary-item">
                <span>إجمالي المنتجات:</span>
                <span>${invoiceData.subtotal.toLocaleString()} ج.م</span>
              </div>
              <div class="summary-item">
                <span>تكلفة الشحن:</span>
                <span>${invoiceData.shippingFee.toLocaleString()} ج.م</span>
              </div>
              <div class="summary-item summary-total">
                <span>السعر + الشحن:</span>
                <span>${invoiceData.total.toLocaleString()} ج.م</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Important Message -->
        <div class="footer-message">
          ⚠️ الرجاء معاينة الأوردر في حضور مندوب الشحن
        </div>

        <!-- Footer -->
        <div class="footer">
          <div style="margin-bottom: 15px;">
            <h3 style="margin-bottom: 10px;">شكراً لثقتك في ${companySettings.companyName}</h3>
            <p style="opacity: 0.9;">نتطلع لخدمتك مرة أخرى</p>
          </div>
          <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
            <div>
              <strong>📞 للاستفسارات:</strong> ${companySettings.companyPhone}
            </div>
            <div>
              <strong>📧 البريد الإلكتروني:</strong> ${companySettings.companyEmail}
            </div>
            <div>
              <strong>📍 العنوان:</strong> ${companySettings.companyAddress}
            </div>
          </div>
        </div>
      </div>
    </body>
    </html>
  `;
};

export const printInvoice = (invoiceData: InvoiceData, companySettings: CompanySettings): void => {
  const printWindow = window.open('', '_blank');
  if (!printWindow) {
    throw new Error("فشل في فتح نافذة الطباعة. يرجى السماح بالنوافذ المنبثقة.");
  }

  const htmlContent = generateInvoiceHTML(invoiceData, companySettings);
  
  printWindow.document.open();
  printWindow.document.write(htmlContent);
  printWindow.document.close();

  // Wait for content to load before printing
  printWindow.onload = function() {
    printWindow.focus();
    printWindow.print();
  };
};
