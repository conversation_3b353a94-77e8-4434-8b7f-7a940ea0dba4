// قاعدة بيانات محلية باستخدام IndexedDB (بدون مكتبات خارجية)
// سهلة التحويل لقاعدة بيانات حقيقية لاحقاً

interface DatabaseSchema {
  employees: any[];
  products: any[];
  orders: any[];
  marketers: any[];
  categories: any[];
  companySettings: any;
  siteSettings: any;
}

class LocalDatabase {
  private dbName = 'AfleetDB';
  private version = 1;
  private db: IDBDatabase | null = null;

  // تهيئة قاعدة البيانات
  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log('🔄 تهيئة قاعدة البيانات المحلية...');
      
      const request = indexedDB.open(this.dbName, this.version);

      request.onerror = () => {
        console.error('❌ فشل في فتح قاعدة البيانات:', request.error);
        reject(request.error);
      };

      request.onsuccess = () => {
        this.db = request.result;
        console.log('✅ تم فتح قاعدة البيانات بنجاح');
        resolve();
      };

      request.onupgradeneeded = (event) => {
        console.log('🔧 إنشاء/تحديث هيكل قاعدة البيانات...');
        const db = (event.target as IDBOpenDBRequest).result;

        // إنشاء الجداول (Object Stores)
        const tables = ['employees', 'products', 'orders', 'marketers', 'categories', 'settings'];
        
        tables.forEach(tableName => {
          if (!db.objectStoreNames.contains(tableName)) {
            const store = db.createObjectStore(tableName, { keyPath: 'id', autoIncrement: true });
            console.log(`✅ تم إنشاء جدول: ${tableName}`);
            
            // إضافة فهارس للبحث السريع
            if (tableName === 'employees') {
              store.createIndex('email', 'email', { unique: true });
              store.createIndex('role', 'role', { unique: false });
            }
            if (tableName === 'orders') {
              store.createIndex('status', 'status', { unique: false });
              store.createIndex('customerPhone', 'customerPhone', { unique: false });
            }
            if (tableName === 'products') {
              store.createIndex('category', 'category', { unique: false });
            }
          }
        });

        // جدول خاص للإعدادات (مفتاح-قيمة)
        if (!db.objectStoreNames.contains('keyvalue')) {
          db.createObjectStore('keyvalue', { keyPath: 'key' });
          console.log('✅ تم إنشاء جدول الإعدادات');
        }
      };
    });
  }

  // حفظ البيانات
  async setItem(table: string, data: any): Promise<void> {
    if (!this.db) throw new Error('قاعدة البيانات غير مهيأة');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([table], 'readwrite');
      const store = transaction.objectStore(table);
      
      const request = store.put(data);
      
      request.onsuccess = () => {
        console.log(`✅ تم حفظ البيانات في ${table}`);
        resolve();
      };
      
      request.onerror = () => {
        console.error(`❌ فشل في حفظ البيانات في ${table}:`, request.error);
        reject(request.error);
      };
    });
  }

  // قراءة البيانات
  async getItem(table: string, id?: any): Promise<any> {
    if (!this.db) throw new Error('قاعدة البيانات غير مهيأة');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([table], 'readonly');
      const store = transaction.objectStore(table);
      
      const request = id ? store.get(id) : store.getAll();
      
      request.onsuccess = () => {
        resolve(request.result);
      };
      
      request.onerror = () => {
        console.error(`❌ فشل في قراءة البيانات من ${table}:`, request.error);
        reject(request.error);
      };
    });
  }

  // حفظ إعدادات (مفتاح-قيمة)
  async setSetting(key: string, value: any): Promise<void> {
    return this.setItem('keyvalue', { key, value });
  }

  // قراءة إعدادات
  async getSetting(key: string): Promise<any> {
    const result = await this.getItem('keyvalue', key);
    return result ? result.value : null;
  }

  // البحث بالفهرس
  async searchByIndex(table: string, indexName: string, value: any): Promise<any[]> {
    if (!this.db) throw new Error('قاعدة البيانات غير مهيأة');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([table], 'readonly');
      const store = transaction.objectStore(table);
      const index = store.index(indexName);
      
      const request = index.getAll(value);
      
      request.onsuccess = () => {
        resolve(request.result);
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  }

  // حذف البيانات
  async deleteItem(table: string, id: any): Promise<void> {
    if (!this.db) throw new Error('قاعدة البيانات غير مهيأة');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([table], 'readwrite');
      const store = transaction.objectStore(table);
      
      const request = store.delete(id);
      
      request.onsuccess = () => {
        resolve();
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  }

  // مسح جدول كامل
  async clearTable(table: string): Promise<void> {
    if (!this.db) throw new Error('قاعدة البيانات غير مهيأة');

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([table], 'readwrite');
      const store = transaction.objectStore(table);
      
      const request = store.clear();
      
      request.onsuccess = () => {
        console.log(`🗑️ تم مسح جدول ${table}`);
        resolve();
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  }

  // تصدير البيانات (للنسخ الاحتياطي)
  async exportData(): Promise<any> {
    const data: any = {};
    const tables = ['employees', 'products', 'orders', 'marketers', 'categories'];
    
    for (const table of tables) {
      try {
        data[table] = await this.getItem(table);
      } catch (error) {
        console.warn(`⚠️ فشل في تصدير ${table}:`, error);
        data[table] = [];
      }
    }
    
    // إضافة الإعدادات
    try {
      const settings = await this.getItem('keyvalue');
      data.settings = settings || [];
    } catch (error) {
      data.settings = [];
    }
    
    return data;
  }

  // استيراد البيانات
  async importData(data: any): Promise<void> {
    console.log('📥 بدء استيراد البيانات...');
    
    for (const [table, items] of Object.entries(data)) {
      if (table === 'settings') {
        // استيراد الإعدادات
        for (const setting of items as any[]) {
          await this.setSetting(setting.key, setting.value);
        }
      } else if (Array.isArray(items)) {
        // استيراد الجداول العادية
        for (const item of items) {
          await this.setItem(table, item);
        }
      }
    }
    
    console.log('✅ تم استيراد جميع البيانات');
  }

  // إغلاق قاعدة البيانات
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
      console.log('🔒 تم إغلاق قاعدة البيانات');
    }
  }
}

// إنشاء مثيل واحد من قاعدة البيانات
export const localDB = new LocalDatabase();

// تهيئة قاعدة البيانات عند التحميل
export const initializeLocalDB = async (): Promise<void> => {
  try {
    await localDB.init();
    console.log('🎉 قاعدة البيانات المحلية جاهزة!');
  } catch (error) {
    console.error('❌ فشل في تهيئة قاعدة البيانات:', error);
    throw error;
  }
};

// دوال مساعدة للتوافق مع الكود الحالي
export const dbGet = async (key: string): Promise<any> => {
  try {
    // محاولة قراءة من IndexedDB أولاً
    const result = await localDB.getSetting(key);
    if (result !== null) {
      return JSON.stringify(result);
    }
    
    // fallback إلى localStorage
    return localStorage.getItem(key);
  } catch (error) {
    console.warn(`⚠️ فشل في قراءة ${key} من IndexedDB، استخدام localStorage`);
    return localStorage.getItem(key);
  }
};

export const dbSet = async (key: string, value: string): Promise<void> => {
  try {
    // محاولة حفظ في IndexedDB أولاً
    const parsedValue = JSON.parse(value);
    await localDB.setSetting(key, parsedValue);
  } catch (error) {
    console.warn(`⚠️ فشل في حفظ ${key} في IndexedDB، استخدام localStorage`);
    localStorage.setItem(key, value);
  }
};

export const dbRemove = async (key: string): Promise<void> => {
  try {
    await localDB.deleteItem('keyvalue', key);
  } catch (error) {
    localStorage.removeItem(key);
  }
};
