// خدمة هجرة البيانات من localStorage إلى IndexedDB
import { localDB } from './localDatabase';
import fallbackData from '@/data/fallback-data.json';

// هجرة البيانات من localStorage إلى IndexedDB
export const migrateFromLocalStorage = async (): Promise<void> => {
  console.log('🔄 بدء هجرة البيانات من localStorage إلى IndexedDB...');
  
  const keysToMigrate = [
    'employees',
    'products', 
    'orders',
    'marketers',
    'categories',
    'companySettings',
    'siteSettings'
  ];

  let migratedCount = 0;

  for (const key of keysToMigrate) {
    try {
      // قراءة من localStorage
      const localData = localStorage.getItem(key);
      
      if (localData) {
        const parsedData = JSON.parse(localData);
        
        // حفظ في IndexedDB
        await localDB.setSetting(key, parsedData);
        console.log(`✅ تم نقل ${key} (${Array.isArray(parsedData) ? parsedData.length : 1} عنصر)`);
        migratedCount++;
      } else {
        // استخدام البيانات الاحتياطية
        const fallbackValue = (fallbackData as any)[key];
        if (fallbackValue) {
          await localDB.setSetting(key, fallbackValue);
          console.log(`📄 تم استخدام البيانات الاحتياطية لـ ${key}`);
          migratedCount++;
        }
      }
    } catch (error) {
      console.error(`❌ فشل في نقل ${key}:`, error);
    }
  }

  console.log(`✅ تم نقل ${migratedCount} من ${keysToMigrate.length} مجموعة بيانات`);
};

// التحقق من وجود بيانات في IndexedDB
export const checkIndexedDBData = async (): Promise<boolean> => {
  try {
    const employees = await localDB.getSetting('employees');
    return employees && employees.length > 0;
  } catch (error) {
    return false;
  }
};

// تهيئة البيانات الأساسية في IndexedDB
export const initializeIndexedDBData = async (): Promise<void> => {
  console.log('🔧 تهيئة البيانات الأساسية في IndexedDB...');
  
  try {
    // التحقق من وجود بيانات
    const hasData = await checkIndexedDBData();
    
    if (!hasData) {
      console.log('📥 لا توجد بيانات، تحميل البيانات الافتراضية...');
      
      // تحميل البيانات الافتراضية
      await localDB.setSetting('employees', fallbackData.employees);
      await localDB.setSetting('companySettings', fallbackData.companySettings);
      await localDB.setSetting('products', fallbackData.products);
      await localDB.setSetting('orders', fallbackData.orders);
      await localDB.setSetting('marketers', fallbackData.marketers);
      await localDB.setSetting('categories', fallbackData.categories);
      
      console.log('✅ تم تحميل البيانات الافتراضية');
    } else {
      console.log('✅ البيانات موجودة في IndexedDB');
    }
  } catch (error) {
    console.error('❌ فشل في تهيئة البيانات:', error);
    throw error;
  }
};

// تصدير البيانات للنسخ الاحتياطي
export const exportAllData = async (): Promise<string> => {
  try {
    const data = await localDB.exportData();
    return JSON.stringify(data, null, 2);
  } catch (error) {
    console.error('❌ فشل في تصدير البيانات:', error);
    throw error;
  }
};

// استيراد البيانات من النسخ الاحتياطي
export const importAllData = async (jsonData: string): Promise<void> => {
  try {
    const data = JSON.parse(jsonData);
    await localDB.importData(data);
    console.log('✅ تم استيراد البيانات بنجاح');
  } catch (error) {
    console.error('❌ فشل في استيراد البيانات:', error);
    throw error;
  }
};

// مسح جميع البيانات (للتطوير)
export const clearAllData = async (): Promise<void> => {
  try {
    const tables = ['employees', 'products', 'orders', 'marketers', 'categories'];
    
    for (const table of tables) {
      await localDB.clearTable(table);
    }
    
    await localDB.clearTable('keyvalue');
    console.log('🗑️ تم مسح جميع البيانات');
  } catch (error) {
    console.error('❌ فشل في مسح البيانات:', error);
    throw error;
  }
};

// دوال للنافذة (للتطوير)
(window as any).migrateData = migrateFromLocalStorage;
(window as any).exportData = exportAllData;
(window as any).importData = importAllData;
(window as any).clearData = clearAllData;
(window as any).checkData = checkIndexedDBData;

console.log('🛠️ دوال إدارة البيانات متاحة:');
console.log('- migrateData() - نقل من localStorage');
console.log('- exportData() - تصدير البيانات');
console.log('- importData(json) - استيراد البيانات');
console.log('- clearData() - مسح البيانات');
console.log('- checkData() - فحص البيانات');
