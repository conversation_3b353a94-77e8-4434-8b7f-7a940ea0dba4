// خدمات التحصيل والمتابعة (مبسطة)

// مفاتيح التخزين المحلي
const STORAGE_KEYS = {
  SHIPPING_COMPANIES: 'shipping_companies',
  PAYMENTS: 'payments',
  RETURNED_PRODUCTS: 'returned_products',
  COLLECTION_ORDERS: 'collection_orders',
};

// تهيئة بيانات التحصيل والمتابعة
export const initializeCollectionData = (): void => {
  console.log("تم تهيئة بيانات التحصيل والمتابعة (وظيفة فارغة)");
};

// الحصول على شركات الشحن
export const getShippingCompanies = () => {
  const storedCompanies = localStorage.getItem("shipping_companies");
  if (storedCompanies) {
    return JSON.parse(storedCompanies);
  }
  return [];
};

// إضافة شركة شحن جديدة
export const addShippingCompany = (company: any) => {
  const companies = getShippingCompanies();
  const newCompany = {
    id: `sc-${Date.now()}`,
    createdAt: new Date().toISOString(),
    ...company
  };
  companies.push(newCompany);
  localStorage.setItem("shipping_companies", JSON.stringify(companies));
  return newCompany;
};

// تحديث شركة شحن
export const updateShippingCompany = (id: string, data: any) => {
  const companies = getShippingCompanies();
  const index = companies.findIndex(c => c.id === id);
  if (index !== -1) {
    companies[index] = { ...companies[index], ...data };
    localStorage.setItem("shipping_companies", JSON.stringify(companies));
    return companies[index];
  }
  return null;
};

// حذف شركة شحن
export const deleteShippingCompany = (id: string) => {
  const companies = getShippingCompanies();
  const filteredCompanies = companies.filter(c => c.id !== id);
  localStorage.setItem("shipping_companies", JSON.stringify(filteredCompanies));
  return true;
};

// إضافة منطقة شحن
export const addShippingZone = (companyId: string, zone: any) => {
  const companies = getShippingCompanies();
  const index = companies.findIndex(c => c.id === companyId);
  if (index !== -1) {
    if (!companies[index].zones) {
      companies[index].zones = [];
    }
    const newZone = {
      id: `sz-${Date.now()}`,
      createdAt: new Date().toISOString(),
      ...zone
    };
    companies[index].zones.push(newZone);
    localStorage.setItem(STORAGE_KEYS.SHIPPING_COMPANIES, JSON.stringify(companies));
    return newZone;
  }
  return null;
};

// تحديث منطقة شحن
export const updateShippingZone = (companyId: string, zoneId: string, data: any) => {
  const companies = getShippingCompanies();
  const companyIndex = companies.findIndex(c => c.id === companyId);
  if (companyIndex !== -1 && companies[companyIndex].zones) {
    const zoneIndex = companies[companyIndex].zones.findIndex(z => z.id === zoneId);
    if (zoneIndex !== -1) {
      companies[companyIndex].zones[zoneIndex] = {
        ...companies[companyIndex].zones[zoneIndex],
        ...data
      };
      localStorage.setItem(STORAGE_KEYS.SHIPPING_COMPANIES, JSON.stringify(companies));
      return companies[companyIndex].zones[zoneIndex];
    }
  }
  return null;
};

// حذف منطقة شحن
export const deleteShippingZone = (companyId: string, zoneId: string) => {
  const companies = getShippingCompanies();
  const companyIndex = companies.findIndex(c => c.id === companyId);
  if (companyIndex !== -1 && companies[companyIndex].zones) {
    companies[companyIndex].zones = companies[companyIndex].zones.filter(z => z.id !== zoneId);
    localStorage.setItem(STORAGE_KEYS.SHIPPING_COMPANIES, JSON.stringify(companies));
    return true;
  }
  return false;
};

// تصدير مناطق الشحن
export const exportShippingZones = (companyId: string) => {
  const companies = getShippingCompanies();
  const company = companies.find(c => c.id === companyId);
  if (company && company.zones) {
    return company.zones;
  }
  return [];
};

// استيراد مناطق الشحن
export const importShippingZones = (companyId: string, zones: any[]) => {
  const companies = getShippingCompanies();
  const companyIndex = companies.findIndex(c => c.id === companyId);
  if (companyIndex !== -1) {
    companies[companyIndex].zones = zones.map(zone => ({
      ...zone,
      id: zone.id || `sz-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: zone.createdAt || new Date().toISOString()
    }));
    localStorage.setItem(STORAGE_KEYS.SHIPPING_COMPANIES, JSON.stringify(companies));
    return companies[companyIndex].zones;
  }
  return null;
};

// نسخ مناطق الشحن
export const copyShippingZones = (sourceCompanyId: string, targetCompanyId: string) => {
  const companies = getShippingCompanies();
  const sourceCompany = companies.find(c => c.id === sourceCompanyId);
  const targetCompanyIndex = companies.findIndex(c => c.id === targetCompanyId);

  if (sourceCompany && sourceCompany.zones && targetCompanyIndex !== -1) {
    const copiedZones = sourceCompany.zones.map(zone => ({
      ...zone,
      id: `sz-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString()
    }));

    if (!companies[targetCompanyIndex].zones) {
      companies[targetCompanyIndex].zones = [];
    }

    companies[targetCompanyIndex].zones = [...companies[targetCompanyIndex].zones, ...copiedZones];
    localStorage.setItem(STORAGE_KEYS.SHIPPING_COMPANIES, JSON.stringify(companies));
    return copiedZones;
  }

  return null;
};

// تحديث أسعار المناطق بشكل جماعي
export const bulkUpdateZonePrices = (companyId: string, priceChange: number, isPercentage: boolean) => {
  const companies = getShippingCompanies();
  const companyIndex = companies.findIndex(c => c.id === companyId);

  if (companyIndex !== -1 && companies[companyIndex].zones) {
    companies[companyIndex].zones = companies[companyIndex].zones.map(zone => {
      const currentPrice = parseFloat(zone.price) || 0;
      let newPrice;

      if (isPercentage) {
        // تغيير بنسبة مئوية
        newPrice = currentPrice + (currentPrice * priceChange / 100);
      } else {
        // تغيير بقيمة ثابتة
        newPrice = currentPrice + priceChange;
      }

      // التأكد من أن السعر لا يقل عن صفر
      newPrice = Math.max(0, newPrice);

      return {
        ...zone,
        price: newPrice.toString()
      };
    });

    localStorage.setItem(STORAGE_KEYS.SHIPPING_COMPANIES, JSON.stringify(companies));
    return companies[companyIndex].zones;
  }

  return null;
};

// الحصول على المحافظات المتاحة
export const getAvailableGovernorates = () => {
  return [
    "القاهرة",
    "الجيزة",
    "الإسكندرية",
    "الدقهلية",
    "البحر الأحمر",
    "البحيرة",
    "الفيوم",
    "الغربية",
    "الإسماعيلية",
    "المنوفية",
    "المنيا",
    "القليوبية",
    "الوادي الجديد",
    "السويس",
    "اسوان",
    "اسيوط",
    "بني سويف",
    "بورسعيد",
    "دمياط",
    "الشرقية",
    "جنوب سيناء",
    "كفر الشيخ",
    "مطروح",
    "الأقصر",
    "قنا",
    "شمال سيناء",
    "سوهاج"
  ];
};

// الحصول على المدن حسب المحافظة
export const getCitiesByGovernorate = (governorate: string) => {
  const citiesMap: Record<string, string[]> = {
    "القاهرة": ["مدينة نصر", "المعادي", "مصر الجديدة", "وسط البلد", "المقطم", "الزمالك", "شبرا", "حلوان", "عين شمس", "المرج", "السلام", "التجمع الخامس", "التجمع الأول", "مدينة الشروق", "مدينة بدر", "مدينة العبور"],
    "الجيزة": ["الدقي", "المهندسين", "العجوزة", "الهرم", "فيصل", "6 أكتوبر", "الشيخ زايد", "حدائق الأهرام", "البدرشين", "العياط", "الصف", "أطفيح", "الواحات البحرية", "منشأة القناطر", "أوسيم", "كرداسة", "أبو النمرس"],
    "الإسكندرية": ["المنتزه", "شرق الإسكندرية", "وسط الإسكندرية", "الجمرك", "العامرية", "العجمي", "برج العرب", "سموحة", "سيدي جابر", "الأزاريطة", "محرم بك", "الشاطبي", "كامب شيزار", "الورديان", "المكس", "أبو قير"],
    "الدقهلية": ["المنصورة", "طلخا", "ميت غمر", "دكرنس", "أجا", "منية النصر", "السنبلاوين", "الكردي", "بني عبيد", "المنزلة", "تمي الأمديد", "الجمالية", "شربين", "المطرية", "بلقاس", "ميت سلسيل", "جمصة", "محلة دمنة", "نبروه"],
    "البحر الأحمر": ["الغردقة", "رأس غارب", "سفاجا", "القصير", "مرسى علم", "شلاتين", "حلايب"],
    "البحيرة": ["دمنهور", "كفر الدوار", "رشيد", "إدكو", "أبو المطامير", "أبو حمص", "الدلنجات", "المحمودية", "الرحمانية", "إيتاي البارود", "حوش عيسى", "شبراخيت", "كوم حمادة", "بدر", "وادي النطرون", "النوبارية الجديدة"],
    "الفيوم": ["الفيوم", "طامية", "سنورس", "إطسا", "إبشواي", "يوسف الصديق"],
    "الغربية": ["طنطا", "المحلة الكبرى", "كفر الزيات", "زفتى", "السنطة", "قطور", "بسيون", "سمنود"],
    "الإسماعيلية": ["الإسماعيلية", "فايد", "القنطرة شرق", "القنطرة غرب", "التل الكبير", "أبو صوير", "القصاصين"],
    "المنوفية": ["شبين الكوم", "منوف", "أشمون", "الباجور", "قويسنا", "بركة السبع", "تلا", "الشهداء", "سرس الليان", "السادات"],
    "المنيا": ["المنيا", "العدوة", "مغاغة", "بني مزار", "مطاي", "سمالوط", "أبو قرقاص", "ملوي", "دير مواس"],
    "القليوبية": ["بنها", "قليوب", "شبرا الخيمة", "القناطر الخيرية", "الخانكة", "كفر شكر", "طوخ", "قها", "العبور", "الخصوص", "شبين القناطر"],
    "الوادي الجديد": ["الخارجة", "باريس", "بلاط", "الفرافرة", "موط"],
    "السويس": ["السويس", "الأربعين", "عتاقة", "الجناين", "فيصل"],
    "اسوان": ["أسوان", "أسوان الجديدة", "دراو", "كوم أمبو", "نصر النوبة", "إدفو", "الرديسية", "البصيلية", "السباعية", "أبو سمبل السياحية"],
    "اسيوط": ["أسيوط", "أسيوط الجديدة", "ديروط", "منفلوط", "القوصية", "أبنوب", "الفتح", "ساحل سليم", "البداري", "صدفا", "الغنايم"],
    "بني سويف": ["بني سويف", "بني سويف الجديدة", "الواسطى", "ناصر", "إهناسيا", "ببا", "سمسطا", "الفشن"],
    "بورسعيد": ["بورسعيد", "بورفؤاد", "العرب", "الضواحي", "المناخ", "الزهور", "الشرق", "الجنوب"],
    "دمياط": ["دمياط", "دمياط الجديدة", "رأس البر", "فارسكور", "الزرقا", "السرو", "الروضة", "كفر سعد", "ميت أبو غالب", "عزبة البرج", "كفر البطيخ"],
    "الشرقية": ["الزقازيق", "العاشر من رمضان", "منيا القمح", "بلبيس", "مشتول السوق", "الإبراهيمية", "ههيا", "أبو حماد", "أبو كبير", "الحسينية", "صان الحجر", "القرين", "كفر صقر", "أولاد صقر", "الصالحية الجديدة", "القنايات", "ديرب نجم"],
    "جنوب سيناء": ["الطور", "شرم الشيخ", "دهب", "نويبع", "طابا", "سانت كاترين", "أبو رديس", "أبو زنيمة", "رأس سدر"],
    "كفر الشيخ": ["كفر الشيخ", "دسوق", "فوه", "مطوبس", "بيلا", "الرياض", "الحامول", "سيدي سالم", "قلين", "سيدي غازي", "بلطيم", "مصيف بلطيم", "برج البرلس"],
    "مطروح": ["مرسى مطروح", "الحمام", "العلمين", "الضبعة", "النجيلة", "سيدي براني", "السلوم", "سيوة"],
    "الأقصر": ["الأقصر", "الأقصر الجديدة", "طيبة الجديدة", "الزينية", "البياضية", "القرنة", "أرمنت", "الطود", "إسنا"],
    "قنا": ["قنا", "قنا الجديدة", "أبو تشت", "نجع حمادي", "دشنا", "الوقف", "قفط", "نقادة", "قوص", "فرشوط"],
    "شمال سيناء": ["العريش", "الشيخ زويد", "رفح", "بئر العبد", "الحسنة", "نخل"],
    "سوهاج": ["سوهاج", "سوهاج الجديدة", "أخميم", "أخميم الجديدة", "البلينا", "المراغة", "المنشاة", "دار السلام", "جرجا", "جهينة الغربية", "ساقلتة", "طما", "طهطا", "العسيرات"]
  };

  return citiesMap[governorate] || [];
};

// إضافة منطقة تغطية
export const addCoverageArea = (companyId: string, coverageArea: any) => {
  const companies = getShippingCompanies();
  const index = companies.findIndex(c => c.id === companyId);
  if (index !== -1) {
    if (!companies[index].coverageAreas) {
      companies[index].coverageAreas = [];
    }
    const newCoverageArea = {
      id: `ca-${Date.now()}`,
      createdAt: new Date().toISOString(),
      ...coverageArea
    };
    companies[index].coverageAreas.push(newCoverageArea);
    localStorage.setItem(STORAGE_KEYS.SHIPPING_COMPANIES, JSON.stringify(companies));
    return newCoverageArea;
  }
  return null;
};

// تحديث منطقة تغطية
export const updateCoverageArea = (companyId: string, areaId: string, data: any) => {
  const companies = getShippingCompanies();
  const companyIndex = companies.findIndex(c => c.id === companyId);
  if (companyIndex !== -1 && companies[companyIndex].coverageAreas) {
    const areaIndex = companies[companyIndex].coverageAreas.findIndex(a => a.id === areaId);
    if (areaIndex !== -1) {
      companies[companyIndex].coverageAreas[areaIndex] = {
        ...companies[companyIndex].coverageAreas[areaIndex],
        ...data,
        updatedAt: new Date().toISOString()
      };
      localStorage.setItem(STORAGE_KEYS.SHIPPING_COMPANIES, JSON.stringify(companies));
      return companies[companyIndex].coverageAreas[areaIndex];
    }
  }
  return null;
};

// حذف منطقة تغطية
export const deleteCoverageArea = (companyId: string, areaId: string) => {
  const companies = getShippingCompanies();
  const companyIndex = companies.findIndex(c => c.id === companyId);
  if (companyIndex !== -1 && companies[companyIndex].coverageAreas) {
    companies[companyIndex].coverageAreas = companies[companyIndex].coverageAreas.filter(a => a.id !== areaId);
    localStorage.setItem(STORAGE_KEYS.SHIPPING_COMPANIES, JSON.stringify(companies));
    return true;
  }
  return false;
};
