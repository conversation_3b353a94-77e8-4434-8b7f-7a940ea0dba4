import React, { Suspense, lazy } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "./contexts/AuthContext";
import { registerServiceWorker } from "./utils/performance";
import ErrorBoundary from "./components/ErrorBoundary";
import { performanceMonitor } from "./utils/monitoring";
import { initializeSecurity, performSecurityAudit } from "./middleware/security";
import { initializeDeliveryOrders, initializeSampleOrders } from "./services/localStorageService";
import { initializeProducts } from "./services/productService";
import { initializeCategories } from "./services/categoryService";
import { initializeEmployees } from "./services/employeeService";
import { initializeUnifiedEmployees } from "./services/unifiedEmployeeService";
import { initializeLocalDB } from "./services/localDatabase";
import { migrateFromLocalStorage, initializeIndexedDBData } from "./services/dataMigration";
import { fixAllImages } from "./services/imageFixService";
import { initializeSiteSettings } from "./services/siteSettingsService";
import { initializeShippingCompanies } from "./services/shippingCompanyService";
import StoreStatusWrapper from "./components/StoreStatusWrapper";

// الصفحات الأساسية - تحميل فوري
import Index from "./pages/Index";
import Login from "./pages/Login";
import NotFound from "./pages/NotFound";

// الصفحات الثقيلة - تحميل كسول
const Dashboard = lazy(() => import("./pages/Dashboard"));
const Categories = lazy(() => import("./pages/Categories"));
const Products = lazy(() => import("./pages/Products"));
const Orders = lazy(() => import("./pages/Orders"));
const AddProduct = lazy(() => import("./pages/AddProduct"));
const EditProduct = lazy(() => import("./pages/EditProduct"));
const ProductDetails = lazy(() => import("./pages/ProductDetails"));
const OrderDetails = lazy(() => import("./pages/OrderDetails"));
const EditOrder = lazy(() => import("./pages/EditOrder"));
const Warehouse = lazy(() => import("./pages/Warehouse"));
const Shipping = lazy(() => import("./pages/Shipping"));
const InDelivery = lazy(() => import("./pages/InDelivery"));
const Archive = lazy(() => import("./pages/Archive"));
const Marketers = lazy(() => import("./pages/Marketers"));
const MarketerDetails = lazy(() => import("./pages/MarketerDetails"));
const Commissions = lazy(() => import("./pages/Commissions"));
const Employees = lazy(() => import("./pages/Employees"));
const ShippingSettings = lazy(() => import("./pages/ShippingSettings"));
const Settings = lazy(() => import("./pages/Settings"));
const SiteSettings = lazy(() => import("./pages/SiteSettings"));

const Notifications = lazy(() => import("./pages/Notifications"));
const Reports = lazy(() => import("./pages/Reports"));

const queryClient = new QueryClient();

// تهيئة طلبات قسم جاري التوصيل
initializeDeliveryOrders();

// تهيئة 20 طلب تجريبي متنوع
initializeSampleOrders();

// تهيئة المنتجات
initializeProducts();

// تهيئة الأقسام
initializeCategories();

// تهيئة قاعدة البيانات المحلية أولاً
initializeLocalDB().then(async () => {
  console.log('🎉 قاعدة البيانات المحلية جاهزة!');

  try {
    // تهيئة البيانات الأساسية
    await initializeIndexedDBData();

    // هجرة البيانات من localStorage إذا لزم الأمر
    await migrateFromLocalStorage();

    console.log('✅ تم تهيئة جميع البيانات بنجاح');
  } catch (error) {
    console.warn('⚠️ خطأ في تهيئة البيانات:', error);
  }

  // تهيئة النظام الموحد للموظفين
  initializeUnifiedEmployees();
}).catch(error => {
  console.error('❌ فشل في تهيئة قاعدة البيانات، استخدام localStorage:', error);

  // fallback إلى النظام الموحد
  initializeUnifiedEmployees();
});

// تهيئة إعدادات الموقع
initializeSiteSettings();

// تهيئة شركات الشحن
initializeShippingCompanies();

// طباعة رسالة تأكيد
console.log("تم تهيئة بيانات قسم جاري التوصيل والمنتجات والأقسام والموظفين وإعدادات الموقع وشركات الشحن بنجاح");

// إصلاح شامل وفوري لجميع الصور
try {
  console.log('🖼️ تشغيل خدمة الإصلاح الشاملة للصور...');

  // إصلاح فوري
  fixAllImages();

  // إصلاح دوري كل 3 ثواني
  const intervalId = setInterval(() => {
    fixAllImages();
  }, 3000);

  // إصلاح عند تغيير localStorage
  const originalSetItem = localStorage.setItem;
  localStorage.setItem = function(key: string, value: string) {
    originalSetItem.call(this, key, value);

    // إصلاح فوري بعد حفظ البيانات
    setTimeout(() => {
      if (['products', 'orders', 'marketers', 'categories', 'employees'].includes(key)) {
        console.log(`🔧 إصلاح تلقائي بعد تحديث ${key}`);
        fixAllImages();
      }
    }, 100);
  };

  // إصلاح عند تحميل الصفحة
  window.addEventListener('load', () => {
    setTimeout(() => {
      fixAllImages();
    }, 1000);
  });

  // إصلاح عند تغيير الصفحة
  window.addEventListener('popstate', () => {
    setTimeout(() => {
      fixAllImages();
    }, 500);
  });

  console.log('✅ تم تفعيل الإصلاح التلقائي الشامل');

  // تنظيف عند إغلاق التطبيق
  window.addEventListener('beforeunload', () => {
    clearInterval(intervalId);
  });

} catch (error) {
  console.warn('⚠️ خطأ في خدمة إصلاح الصور:', error);
}

// اختبار فوري للنظام
console.log('🧪 اختبار سريع للنظام...');

// اختبار نظام التخزين
try {
  localStorage.setItem('test', 'test');
  localStorage.removeItem('test');
  console.log('✅ localStorage يعمل');
} catch (e) {
  console.log('❌ localStorage لا يعمل - سيتم استخدام البيانات الاحتياطية');
}

// اختبار البيانات الاحتياطية
try {
  import('./services/fallbackService').then(({ FallbackStorageService }) => {
    const status = FallbackStorageService.getStorageStatus();
    console.log('📊 حالة التخزين:', status);

    // إضافة دوال للنافذة
    (window as any).showStorageStatus = () => {
      console.log('📊 حالة التخزين:', FallbackStorageService.getStorageStatus());
    };

    (window as any).testLogin = () => {
      import('./services/employeeService').then(({ loginEmployee, getEmployees }) => {
        console.log('🧪 اختبار تسجيل الدخول...');
        const employees = getEmployees();
        console.log('👥 الموظفين المتاحين:', employees.length);
        console.table(employees.map(emp => ({ name: emp.name, email: emp.email, password: emp.password })));

        const admin = loginEmployee('admin', 'admin123');
        console.log('🔑 نتيجة تسجيل الدخول:', admin ? `✅ نجح - ${admin.name}` : '❌ فشل');
      });
    };

    console.log('🛠️ للاختبار: اكتب testLogin() في Console');
    console.log('📊 لعرض حالة التخزين: اكتب showStorageStatus() في Console');
  });
} catch (error) {
  console.error('❌ خطأ في تحميل خدمة التخزين:', error);
}

// تعطيل إعادة التحميل التلقائي
if (import.meta.hot) {
  import.meta.hot.accept(() => {});
  import.meta.hot.dispose(() => {});
}

// تسجيل Service Worker للأداء
registerServiceWorker();

// تهيئة الأمان مع معالجة الأخطاء
try {
  initializeSecurity();
  console.log('✅ تم تهيئة الأمان بنجاح');
} catch (error) {
  console.warn('⚠️ خطأ في تهيئة الأمان:', error);
}

// فحص الأمان مع تأخير للسماح بتطبيق الـ headers
setTimeout(() => {
  try {
    const securityIssues = performSecurityAudit();
    if (securityIssues.length === 0) {
      console.log('✅ فحص الأمان مكتمل - لا توجد مشاكل');
    } else {
      console.warn('⚠️ مشاكل أمان تم اكتشافها:', securityIssues);
    }
  } catch (error) {
    console.warn('⚠️ خطأ في فحص الأمان:', error);
  }
}, 1000); // تأخير ثانية واحدة

// Loading component للصفحات الكسولة
const PageLoader = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="flex flex-col items-center space-y-4">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      <p className="text-gray-600">جاري التحميل...</p>
    </div>
  </div>
);

const App = () => (
  <ErrorBoundary>
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
          <StoreStatusWrapper>
            <Suspense fallback={<PageLoader />}>
              <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/login" element={<Login />} />

            {/* Dashboard Routes */}
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/categories" element={<Categories />} />
            <Route path="/products" element={<Products />} />
            <Route path="/orders" element={<Orders />} />
            <Route path="/add-product" element={<AddProduct />} />
            <Route path="/edit-product/:id" element={<EditProduct />} />
            <Route path="/product/:id" element={<ProductDetails />} />
            <Route path="/order-details/:id" element={<OrderDetails />} />
            <Route path="/edit-order/:id" element={<EditOrder />} />

            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="/warehouse" element={<Warehouse />} />
            <Route path="/shipping" element={<Shipping />} />
            <Route path="/delivery" element={<InDelivery />} />
            <Route path="/archive" element={<Archive />} />
            <Route path="/marketers" element={<Marketers />} />
            <Route path="/marketer/:id" element={<MarketerDetails />} />
            <Route path="/commissions" element={<Commissions />} />
            <Route path="/shipping-settings" element={<ShippingSettings />} />
            <Route path="/reports" element={<Reports />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/site-settings" element={<SiteSettings />} />
            <Route path="/employees" element={<Employees />} />

            <Route path="/notifications" element={<Notifications />} />
            <Route path="*" element={<NotFound />} />
              </Routes>
            </Suspense>
          </StoreStatusWrapper>
          </BrowserRouter>
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  </ErrorBoundary>
);

export default App;
