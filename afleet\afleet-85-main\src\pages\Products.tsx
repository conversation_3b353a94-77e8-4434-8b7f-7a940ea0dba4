
import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Plus, Search, Filter, Edit, Trash2, FileCheck, ExternalLink, Package, AlertCircle, Eye, EyeOff } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { getProducts, deleteProduct, toggleProductVisibility, Product } from "@/services/productService";
import { toast } from "sonner";
import { fixProductImages } from "@/services/imageFixService";

// Product categories for filter dropdown
const categories = ["الكل", "ملابس", "أحذية", "إكسسوارات", "حقائب", "إلكترونيات"];

const ITEMS_PER_PAGE = 20;

const Products = () => {
  const { hasPermission } = useAuth();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("الكل");
  const [allProducts, setAllProducts] = useState<Product[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalStock, setTotalStock] = useState(0);

  // دالة لتحديث المنتجات
  const refreshProducts = () => {
    const products = getProducts();
    setAllProducts(products);

    // حساب إجمالي المخزون
    const stockSum = products.reduce((sum, product) => sum + product.stock, 0);
    setTotalStock(stockSum);
  };

  // جلب المنتجات عند تحميل الصفحة وحساب إجمالي المخزون
  useEffect(() => {
    // إصلاح الصور أولاً
    fixProductImages();
    refreshProducts();
  }, []);

  // تحديث المنتجات عند العودة للصفحة
  useEffect(() => {
    const handleFocus = () => {
      refreshProducts();
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, []);

  // Filter products based on search term and category
  const filteredProducts = allProducts.filter((product) => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          product.driveLink.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "الكل" ||
                            product.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // Calculate pagination
  useEffect(() => {
    setTotalPages(Math.ceil(filteredProducts.length / ITEMS_PER_PAGE));
    setCurrentPage(1); // Reset to first page when filters change
  }, [filteredProducts.length]);

  // Get current page items
  const getCurrentPageItems = () => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return filteredProducts.slice(startIndex, endIndex);
  };

  // Generate page numbers for pagination
  const getPaginationItems = () => {
    let pages = [];
    const maxVisiblePages = 5;

    // Always show first page
    pages.push(1);

    if (totalPages <= maxVisiblePages) {
      // If we have 5 or fewer pages, show them all
      for (let i = 2; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Complex pagination with ellipsis
      if (currentPage <= 3) {
        // Near the start
        pages = [1, 2, 3, 4, '...', totalPages];
      } else if (currentPage >= totalPages - 2) {
        // Near the end
        pages = [1, '...', totalPages - 3, totalPages - 2, totalPages - 1, totalPages];
      } else {
        // Somewhere in the middle
        pages = [1, '...', currentPage - 1, currentPage, currentPage + 1, '...', totalPages];
      }
    }

    return pages;
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    window.scrollTo(0, 0);
  };

  const handleDeleteProduct = (id: string) => {
    // الحصول على المنتج قبل حذفه لتحديث إجمالي المخزون
    const productToDelete = allProducts.find(product => product.id === id);

    // حذف المنتج من التخزين المحلي
    deleteProduct(id);

    // تحديث حالة المنتجات في الواجهة
    const updatedProducts = allProducts.filter(product => product.id !== id);
    setAllProducts(updatedProducts);

    // تحديث إجمالي المخزون
    if (productToDelete) {
      setTotalStock(prevTotal => prevTotal - productToDelete.stock);
    }
  };

  const handleEditProduct = (id: string) => {
    navigate(`/edit-product/${id}`);
  };

  // تبديل حالة إخفاء/إظهار المنتج
  const handleToggleVisibility = (productId: string) => {
    const updatedProduct = toggleProductVisibility(productId);
    if (updatedProduct) {
      setAllProducts(allProducts.map(p =>
        p.id === productId ? updatedProduct : p
      ));

      const status = updatedProduct.isHidden ? "مخفي من المتجر" : "ظاهر في المتجر";
      toast.success(`تم تغيير حالة المنتج إلى: ${status}`);
    } else {
      toast.error("حدث خطأ أثناء تغيير حالة المنتج");
    }
  };

  // تم إزالة وظائف عرض المنتج لأننا أزلنا الزر المرتبط بها

  const handleAddProduct = () => {
    navigate("/add-product");
  };

  const canManageProducts = hasPermission("products", "edit") || hasPermission("all", "all");

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold">المنتجات</h1>
            <p className="text-muted-foreground">إدارة وعرض جميع منتجات المتجر</p>
          </div>
          <div className="flex flex-wrap gap-3">
            {canManageProducts && (
              <Button onClick={handleAddProduct}>
                <Plus className="ml-2 h-4 w-4" />
                إضافة منتج
              </Button>
            )}
          </div>
        </div>

        {/* بطاقات إحصائية */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* إجمالي المنتجات */}
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600 mb-1">إجمالي المنتجات</p>
                  <h3 className="text-2xl font-bold">{allProducts.length}</h3>
                  <p className="text-xs text-blue-600 mt-1">منتج متاح في المتجر</p>
                </div>
                <div className="h-12 w-12 bg-blue-500 rounded-full flex items-center justify-center text-white">
                  <Package className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* إجمالي المخزون */}
          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600 mb-1">إجمالي المخزون</p>
                  <h3 className="text-2xl font-bold">{totalStock}</h3>
                  <p className="text-xs text-green-600 mt-1">قطعة متاحة في المخزن</p>
                </div>
                <div className="h-12 w-12 bg-green-500 rounded-full flex items-center justify-center text-white">
                  <FileCheck className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* المنتجات منخفضة المخزون */}
          <Card className="bg-gradient-to-br from-amber-50 to-amber-100 border-amber-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-amber-600 mb-1">منتجات منخفضة المخزون</p>
                  <h3 className="text-2xl font-bold">{allProducts.filter(p => p.stock < 20).length}</h3>
                  <p className="text-xs text-amber-600 mt-1">منتج بحاجة إلى تجديد المخزون</p>
                </div>
                <div className="h-12 w-12 bg-amber-500 rounded-full flex items-center justify-center text-white">
                  <AlertCircle className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* المنتجات المخفية */}
          <Card className="bg-gradient-to-br from-red-50 to-red-100 border-red-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-red-600 mb-1">منتجات مخفية</p>
                  <h3 className="text-2xl font-bold">{allProducts.filter(p => p.isHidden).length}</h3>
                  <p className="text-xs text-red-600 mt-1">منتج مخفي من المتجر</p>
                </div>
                <div className="h-12 w-12 bg-red-500 rounded-full flex items-center justify-center text-white">
                  <EyeOff className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>قائمة المنتجات</CardTitle>
            <CardDescription>
              اعرض وأدر قائمة منتجاتك وتحكم في المخزون والأسعار
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute top-2.5 right-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="ابحث عن منتج..."
                  className="pr-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div className="flex flex-row gap-2">
                <div className="relative">
                  <select
                    className="w-full h-10 pl-10 pr-3 rounded-md border border-input bg-background appearance-none"
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                  >
                    {categories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                  <Filter className="absolute top-2.5 right-3 h-4 w-4 text-muted-foreground" />
                </div>
                {canManageProducts && (
                  <Button variant="outline" size="icon">
                    <FileCheck className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>

            <div className="rounded-md border overflow-hidden">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[100px] text-center">الصورة</TableHead>
                      <TableHead className="text-center">اسم المنتج</TableHead>
                      <TableHead className="text-center">رابط جوجل درايف</TableHead>
                      <TableHead className="text-center">السعر</TableHead>
                      <TableHead className="text-center">الخصم</TableHead>
                      <TableHead className="text-center">العمولة</TableHead>
                      <TableHead className="text-center">القسم</TableHead>
                      <TableHead className="text-center">المخزون</TableHead>
                      <TableHead className="text-center">الحالة</TableHead>
                      {canManageProducts && <TableHead className="w-[120px] text-center">الإجراءات</TableHead>}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getCurrentPageItems().length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={canManageProducts ? 10 : 9}
                          className="h-24 text-center"
                        >
                          لا توجد منتجات متطابقة مع البحث
                        </TableCell>
                      </TableRow>
                    ) : (
                      getCurrentPageItems().map((product) => (
                        <TableRow key={product.id}>
                          <TableCell className="text-center">
                            <div className="flex justify-center">
                              <img
                                src={product.thumbnail}
                                alt={product.name}
                                className="w-10 h-10 rounded object-cover"
                              />
                            </div>
                          </TableCell>
                          <TableCell className="font-medium text-center">
                            {product.name}
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="flex justify-center">
                              <a
                                href={product.driveLink}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="flex items-center text-blue-500 hover:text-blue-700"
                              >
                                <ExternalLink className="h-3 w-3 ml-1" /> عرض المجلد
                              </a>
                            </div>
                          </TableCell>
                          <TableCell className="text-center">{product.price} ج.م</TableCell>
                          <TableCell className="text-center">
                            {product.discount > 0 ? `${product.discount}%` : "-"}
                          </TableCell>
                          <TableCell className="text-center">{product.commission} ج.م</TableCell>
                          <TableCell className="text-center">{product.category}</TableCell>
                          <TableCell className="text-center">
                            <span className={product.stock < 20 ? "text-red-500" : "text-green-600"}>
                              {product.stock}
                            </span>
                          </TableCell>
                          <TableCell className="text-center">
                            <div className="flex items-center justify-center">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleToggleVisibility(product.id)}
                                className={`flex items-center gap-2 px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 ${
                                  product.isHidden
                                    ? 'bg-red-100 text-red-700 hover:bg-red-200'
                                    : 'bg-green-100 text-green-700 hover:bg-green-200'
                                }`}
                              >
                                {product.isHidden ? (
                                  <>
                                    <EyeOff className="h-3 w-3" />
                                    مخفي
                                  </>
                                ) : (
                                  <>
                                    <Eye className="h-3 w-3" />
                                    ظاهر
                                  </>
                                )}
                              </Button>
                            </div>
                          </TableCell>
                          {canManageProducts && (
                            <TableCell className="text-center">
                              <div className="flex items-center justify-center gap-2">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleEditProduct(product.id)}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="text-red-500 hover:text-red-600"
                                  onClick={() => handleDeleteProduct(product.id)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          )}
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>

            {totalPages > 1 && (
              <div className="mt-6">
                <Pagination>
                  <PaginationContent>
                    {currentPage > 1 && (
                      <PaginationItem>
                        <PaginationPrevious
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(currentPage - 1);
                          }}
                        />
                      </PaginationItem>
                    )}

                    {getPaginationItems().map((page, index) => (
                      <React.Fragment key={index}>
                        {page === '...' ? (
                          <PaginationItem>
                            <PaginationEllipsis />
                          </PaginationItem>
                        ) : (
                          <PaginationItem>
                            <PaginationLink
                              href="#"
                              isActive={page === currentPage}
                              onClick={(e) => {
                                e.preventDefault();
                                handlePageChange(page as number);
                              }}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        )}
                      </React.Fragment>
                    ))}

                    {currentPage < totalPages && (
                      <PaginationItem>
                        <PaginationNext
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(currentPage + 1);
                          }}
                        />
                      </PaginationItem>
                    )}
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default Products;
