import { Employee, SystemSection, DEFAULT_ROLES } from "@/types/employeeTypes";
import { DEFAULT_PERMISSIONS } from "@/types/employee";
import { getStorageItem, setStorageItem, removeStorageItem, initializeFallbackData } from "./fallbackService";

// مفاتيح التخزين المحلي
const STORAGE_KEYS = {
  EMPLOYEES: "employees",
  CURRENT_EMPLOYEE: "current_employee",
};

// بيانات الموظفين الافتراضية
const defaultEmployees: Employee[] = [
  {
    id: "emp-1",
    name: "أحمد محمد",
    email: "<EMAIL>",
    password: "admin123", // في التطبيق الحقيقي، يجب تشفير كلمات المرور
    phone: "01012345678",
    role: "admin",
    accessibleSections: DEFAULT_ROLES.admin.sections,
    isActive: true,
    createdAt: new Date().toISOString(),
  },
  {
    id: "emp-2",
    name: "محمد علي",
    email: "<EMAIL>",
    password: "sales123",
    phone: "01112345678",
    role: "sales",
    accessibleSections: DEFAULT_ROLES.sales.sections,
    isActive: true,
    createdAt: new Date().toISOString(),
  },
  {
    id: "emp-3",
    name: "سارة أحمد",
    email: "<EMAIL>",
    password: "warehouse123",
    phone: "01212345678",
    role: "warehouse",
    accessibleSections: DEFAULT_ROLES.warehouse.sections,
    isActive: true,
    createdAt: new Date().toISOString(),
  },
  {
    id: "emp-4",
    name: "خالد محمود",
    email: "<EMAIL>",
    password: "shipping123",
    phone: "01312345678",
    role: "shipping",
    accessibleSections: DEFAULT_ROLES.shipping.sections,
    isActive: true,
    createdAt: new Date().toISOString(),
  },
  {
    id: "emp-5",
    name: "فاطمة علي",
    email: "<EMAIL>",
    password: "delivery123",
    phone: "01412345678",
    role: "delivery",
    accessibleSections: DEFAULT_ROLES.delivery.sections,
    isActive: true,
    createdAt: new Date().toISOString(),
  },
];

// إعادة تعيين بيانات الموظفين (للتطوير)
export const resetEmployeesData = (): void => {
  console.log('🔄 بدء إعادة تعيين بيانات الموظفين...');
  removeStorageItem(STORAGE_KEYS.EMPLOYEES);
  removeStorageItem(STORAGE_KEYS.CURRENT_EMPLOYEE);
  setStorageItem(STORAGE_KEYS.EMPLOYEES, JSON.stringify(defaultEmployees));
  console.log("🔄 تم إعادة تعيين بيانات الموظفين");
  console.log("🔄 البيانات الجديدة:", defaultEmployees);
};

// مسح localStorage بالكامل (للتطوير)
export const clearAllData = (): void => {
  console.log('🗑️ مسح جميع البيانات...');
  localStorage.clear();
  console.log('🗑️ تم مسح جميع البيانات');
  location.reload();
};

// فرض حفظ البيانات (للتطوير)
export const forceInitializeEmployees = (): void => {
  console.log('🔧 فرض تهيئة بيانات الموظفين...');

  // مسح البيانات القديمة
  localStorage.removeItem(STORAGE_KEYS.EMPLOYEES);
  localStorage.removeItem(STORAGE_KEYS.CURRENT_EMPLOYEE);

  // حفظ البيانات الجديدة
  try {
    localStorage.setItem(STORAGE_KEYS.EMPLOYEES, JSON.stringify(defaultEmployees));
    console.log('✅ تم حفظ البيانات بنجاح');

    // التحقق من الحفظ
    const saved = localStorage.getItem(STORAGE_KEYS.EMPLOYEES);
    if (saved) {
      const parsed = JSON.parse(saved);
      console.log('✅ تم التحقق من البيانات المحفوظة:', parsed.length, 'موظف');
      console.table(parsed);
    } else {
      console.error('❌ فشل في حفظ البيانات');
    }
  } catch (error) {
    console.error('❌ خطأ في حفظ البيانات:', error);
  }
};

// تهيئة بيانات الموظفين في التخزين المحلي
export const initializeEmployees = (): void => {
  console.log('🔄 بدء تهيئة بيانات الموظفين...');
  console.log('🔄 البيانات الافتراضية:', defaultEmployees.length, 'موظف');
  console.log('🔑 بيانات تسجيل الدخول المتاحة:');
  defaultEmployees.forEach(emp => {
    console.log(`   👤 ${emp.name} | 📧 ${emp.email} | 🔑 ${emp.password}`);
  });

  // تهيئة نظام التخزين الاحتياطي أولاً
  initializeFallbackData();

  const storedEmployees = getStorageItem(STORAGE_KEYS.EMPLOYEES);
  console.log('🔍 البيانات الموجودة:', storedEmployees ? 'موجودة' : 'غير موجودة');

  if (!storedEmployees) {
    console.log('📝 حفظ البيانات الافتراضية...');
    try {
      setStorageItem(STORAGE_KEYS.EMPLOYEES, JSON.stringify(defaultEmployees));
      console.log("✅ تم تهيئة بيانات الموظفين الافتراضية");

      // التحقق من الحفظ
      const verification = getStorageItem(STORAGE_KEYS.EMPLOYEES);
      if (!verification) {
        console.error('❌ فشل في حفظ البيانات');
        // استخدام البيانات من الذاكرة
        (window as any).tempEmployees = defaultEmployees;
        console.log('💾 تم حفظ البيانات في الذاكرة المؤقتة');
      }
    } catch (error) {
      console.error('❌ خطأ في حفظ البيانات:', error);
      (window as any).tempEmployees = defaultEmployees;
      console.log('💾 تم حفظ البيانات في الذاكرة المؤقتة');
    }
  }

  // عرض جميع الموظفين المتاحين للتطوير
  const employees = getEmployees();
  console.log('👥 الموظفين المتاحين للدخول:');
  employees.forEach(emp => {
    console.log(`👤 الاسم: ${emp.name} | 🔑 كلمة المرور: ${emp.password} | 📧 الإيميل: ${emp.email} | ✅ ${emp.isActive ? 'نشط' : 'غير نشط'}`);
    if (emp.accessibleSections && emp.accessibleSections.length > 0) {
      console.log(`   🔐 الأقسام المتاحة: ${emp.accessibleSections.join(', ')}`);
    }
  });
  console.log('💡 يمكن تسجيل الدخول بالاسم أو البريد الإلكتروني + كلمة المرور');

  // إضافة دالة عامة للنافذة للتطوير
  (window as any).resetEmployees = resetEmployeesData;
  (window as any).clearAllData = clearAllData;
  (window as any).forceInitialize = forceInitializeEmployees;
  (window as any).testLogin = (identifier: string, password: string) => {
    console.log('🧪 اختبار تسجيل الدخول...');
    const result = loginEmployee(identifier, password);
    console.log('🧪 النتيجة:', result ? `نجح - ${result.name}` : 'فشل');
    return result;
  };
  (window as any).testAllEmployees = () => {
    console.log('🧪 اختبار تسجيل دخول جميع الموظفين...');
    const employees = getEmployees();
    employees.forEach(emp => {
      console.log(`🧪 اختبار: ${emp.name} (${emp.email})`);
      const result = loginEmployee(emp.email, emp.password);
      console.log(`   ${result ? '✅ نجح' : '❌ فشل'}`);
    });
  };
  (window as any).showEmployees = () => {
    console.log('👥 عرض جميع الموظفين:');
    const employees = getEmployees();
    console.table(employees);
    return employees;
  };
  (window as any).testSimpleLogin = (identifier: string, password: string) => {
    console.log('🧪 اختبار تسجيل دخول مبسط...');
    const result = simpleLogin(identifier, password);
    console.log('🧪 النتيجة:', result ? `نجح - ${result.name}` : 'فشل');
    return result;
  };
  (window as any).forceLogin = () => {
    console.log('🔧 فرض تسجيل دخول الأدمن...');
    const admin = { id: "1", name: "أحمد محمد", email: "<EMAIL>", password: "admin123", role: "admin", accessibleSections: ["dashboard", "products", "orders"], isActive: true, createdAt: new Date().toISOString() };
    setCurrentEmployee(admin as Employee);
    console.log('✅ تم فرض تسجيل دخول الأدمن');
    return admin;
  };
  console.log('🛠️ للتطوير: اكتب resetEmployees() في Console لإعادة تعيين البيانات');
  console.log('🗑️ لمسح كل شيء: اكتب clearAllData() في Console');
  console.log('👥 لعرض الموظفين: اكتب showEmployees() في Console');
  console.log('🧪 للاختبار: اكتب testLogin("<EMAIL>", "admin123") في Console');
  console.log('🧪 لاختبار الجميع: اكتب testAllEmployees() في Console');
};

// الحصول على جميع الموظفين
export const getEmployees = (): Employee[] => {
  // محاولة الحصول على البيانات من نظام التخزين المختلط
  const storedEmployees = getStorageItem(STORAGE_KEYS.EMPLOYEES);
  console.log('🔍 البيانات المخزنة:', storedEmployees ? 'موجودة' : 'غير موجودة');

  if (storedEmployees) {
    try {
      const parsed = JSON.parse(storedEmployees);
      console.log('🔍 البيانات المحللة:', parsed.length, 'موظف');
      return parsed;
    } catch (error) {
      console.error('❌ خطأ في تحليل البيانات:', error);
    }
  }

  // محاولة الحصول على البيانات من الذاكرة المؤقتة
  const tempEmployees = (window as any).tempEmployees;
  if (tempEmployees) {
    console.log('💾 استخدام البيانات من الذاكرة المؤقتة');
    return tempEmployees;
  }

  // إذا لم تكن هناك بيانات، استخدم البيانات الافتراضية
  console.log('🔍 استخدام البيانات الافتراضية');
  return defaultEmployees;
};

// الحصول على موظف بواسطة المعرف
export const getEmployeeById = (id: string): Employee | undefined => {
  const employees = getEmployees();
  return employees.find((employee) => employee.id === id);
};

// الحصول على موظف بواسطة البريد الإلكتروني
export const getEmployeeByEmail = (email: string): Employee | undefined => {
  const employees = getEmployees();
  return employees.find((employee) => employee.email === email);
};

// الحصول على موظف بواسطة الاسم
export const getEmployeeByName = (name: string): Employee | undefined => {
  const employees = getEmployees();
  return employees.find((employee) => employee.name.toLowerCase() === name.toLowerCase());
};

// الحصول على موظف بواسطة الاسم أو الإيميل
export const getEmployeeByNameOrEmail = (identifier: string): Employee | undefined => {
  const employees = getEmployees();
  const lowerIdentifier = identifier.toLowerCase().trim();

  console.log('🔍 البحث عن:', lowerIdentifier);
  console.log('🔍 جميع الموظفين:', employees.map(emp => ({ name: emp.name.toLowerCase(), email: emp.email.toLowerCase(), password: emp.password })));

  // البحث بالإيميل أولاً
  let employee = employees.find((emp) => emp.email.toLowerCase() === lowerIdentifier);
  console.log('🔍 البحث بالإيميل:', employee ? 'موجود' : 'غير موجود');

  // إذا لم يوجد، ابحث بالاسم
  if (!employee) {
    employee = employees.find((emp) => emp.name.toLowerCase() === lowerIdentifier);
    console.log('🔍 البحث بالاسم:', employee ? 'موجود' : 'غير موجود');
  }

  return employee;
};

// إضافة موظف جديد
export const addEmployee = (employee: Omit<Employee, "id" | "createdAt">): Employee => {
  const employees = getEmployees();

  // التحقق من عدم وجود موظف بنفس البريد الإلكتروني
  const existingEmployee = employees.find((emp) => emp.email === employee.email);
  if (existingEmployee) {
    throw new Error("البريد الإلكتروني مستخدم بالفعل");
  }

  // إنشاء موظف جديد
  const newEmployee: Employee = {
    ...employee,
    id: `emp-${Date.now()}`,
    createdAt: new Date().toISOString(),
  };

  // إضافة الموظف إلى القائمة
  employees.push(newEmployee);
  setStorageItem(STORAGE_KEYS.EMPLOYEES, JSON.stringify(employees));

  return newEmployee;
};

// تحديث بيانات موظف
export const updateEmployee = (id: string, data: Partial<Employee>): Employee | null => {
  const employees = getEmployees();
  const index = employees.findIndex((employee) => employee.id === id);

  if (index === -1) {
    return null;
  }

  // التحقق من عدم وجود موظف آخر بنفس البريد الإلكتروني
  if (data.email && data.email !== employees[index].email) {
    const existingEmployee = employees.find((emp) => emp.email === data.email);
    if (existingEmployee) {
      throw new Error("البريد الإلكتروني مستخدم بالفعل");
    }
  }

  // تحديث بيانات الموظف
  employees[index] = {
    ...employees[index],
    ...data,
    updatedAt: new Date().toISOString(),
  };

  setStorageItem(STORAGE_KEYS.EMPLOYEES, JSON.stringify(employees));

  // إذا كان الموظف المحدث هو الموظف الحالي، قم بتحديث بياناته في التخزين المحلي
  const currentEmployee = getCurrentEmployee();
  if (currentEmployee && currentEmployee.id === id) {
    setCurrentEmployee(employees[index]);
  }

  return employees[index];
};

// حذف موظف
export const deleteEmployee = (id: string): boolean => {
  const employees = getEmployees();

  // العثور على الموظف المراد حذفه
  const employeeToDelete = employees.find(emp => emp.id === id);

  if (!employeeToDelete) {
    console.error('❌ لم يتم العثور على الموظف');
    return false;
  }

  // حماية مدير النظام من الحذف
  if (employeeToDelete.role === 'admin' && employeeToDelete.email === '<EMAIL>') {
    console.error('🚫 لا يمكن حذف مدير النظام الرئيسي');
    throw new Error('لا يمكن حذف مدير النظام الرئيسي');
  }

  // التحقق من أنه ليس آخر مدير في النظام
  const adminCount = employees.filter(emp => emp.role === 'admin' && emp.isActive).length;
  if (employeeToDelete.role === 'admin' && adminCount <= 1) {
    console.error('🚫 لا يمكن حذف آخر مدير في النظام');
    throw new Error('لا يمكن حذف آخر مدير في النظام. يجب وجود مدير واحد على الأقل');
  }

  const filteredEmployees = employees.filter((employee) => employee.id !== id);

  if (filteredEmployees.length === employees.length) {
    return false;
  }

  setStorageItem(STORAGE_KEYS.EMPLOYEES, JSON.stringify(filteredEmployees));
  console.log(`✅ تم حذف الموظف: ${employeeToDelete.name}`);
  return true;
};

// التحقق من إمكانية حذف موظف
export const canDeleteEmployee = (id: string): { canDelete: boolean; reason?: string } => {
  const employees = getEmployees();
  const employeeToCheck = employees.find(emp => emp.id === id);

  if (!employeeToCheck) {
    return { canDelete: false, reason: 'الموظف غير موجود' };
  }

  // حماية مدير النظام الرئيسي
  if (employeeToCheck.role === 'admin' && employeeToCheck.email === '<EMAIL>') {
    return { canDelete: false, reason: 'لا يمكن حذف مدير النظام الرئيسي' };
  }

  // التحقق من أنه ليس آخر مدير
  const adminCount = employees.filter(emp => emp.role === 'admin' && emp.isActive).length;
  if (employeeToCheck.role === 'admin' && adminCount <= 1) {
    return { canDelete: false, reason: 'لا يمكن حذف آخر مدير في النظام' };
  }

  return { canDelete: true };
};

// تسجيل دخول موظف مبسط (للاختبار)
export const simpleLogin = (identifier: string, password: string): Employee | null => {
  console.log('🔍 تسجيل دخول مبسط:', { identifier, password });

  // بيانات ثابتة للاختبار
  const testEmployees = [
    { id: "1", name: "أحمد محمد", email: "<EMAIL>", password: "admin123", role: "admin", accessibleSections: ["dashboard", "products", "orders"], isActive: true, createdAt: new Date().toISOString() },
    { id: "2", name: "محمد علي", email: "<EMAIL>", password: "sales123", role: "sales", accessibleSections: ["dashboard", "products"], isActive: true, createdAt: new Date().toISOString() },
    { id: "3", name: "سارة أحمد", email: "<EMAIL>", password: "warehouse123", role: "warehouse", accessibleSections: ["dashboard", "warehouse"], isActive: true, createdAt: new Date().toISOString() }
  ];

  console.log('🔍 البحث في البيانات الثابتة...');

  // البحث بالإيميل
  let employee = testEmployees.find(emp => emp.email.toLowerCase() === identifier.toLowerCase());

  // البحث بالاسم إذا لم يوجد
  if (!employee) {
    employee = testEmployees.find(emp => emp.name.toLowerCase() === identifier.toLowerCase());
  }

  console.log('🔍 الموظف الموجود:', employee);

  if (!employee) {
    console.error('❌ لم يتم العثور على موظف');
    return null;
  }

  if (employee.password !== password) {
    console.error('❌ كلمة المرور غير صحيحة');
    return null;
  }

  console.log('✅ تسجيل دخول ناجح:', employee.name);
  setCurrentEmployee(employee as Employee);
  return employee as Employee;
};

// تسجيل دخول موظف (بالاسم أو الإيميل)
export const loginEmployee = (identifier: string, password: string): Employee | null => {
  console.log('🔍 البحث عن موظف بالاسم أو الإيميل:', identifier);

  // استخدام التسجيل المبسط أولاً
  const simpleResult = simpleLogin(identifier, password);
  if (simpleResult) {
    return simpleResult;
  }

  const employee = getEmployeeByNameOrEmail(identifier);
  console.log('🔍 الموظف الموجود:', employee ? { name: employee.name, email: employee.email, isActive: employee.isActive } : 'غير موجود');

  if (!employee) {
    console.error('❌ لم يتم العثور على موظف بهذا الاسم أو الإيميل');
    return null;
  }

  if (employee.password !== password) {
    console.error('❌ كلمة المرور غير صحيحة');
    console.log('🔍 كلمة المرور المحفوظة:', employee.password);
    console.log('🔍 كلمة المرور المدخلة:', password);
    return null;
  }

  if (!employee.isActive) {
    console.error('❌ الحساب غير نشط');
    throw new Error("الحساب غير نشط");
  }

  console.log('✅ تسجيل دخول ناجح للموظف:', employee.name);

  // تحديث تاريخ آخر تسجيل دخول
  const updatedEmployee = updateEmployee(employee.id, {
    lastLogin: new Date().toISOString(),
  });

  // حفظ الموظف الحالي في التخزين المحلي
  if (updatedEmployee) {
    setCurrentEmployee(updatedEmployee);
  }

  return updatedEmployee;
};

// تسجيل خروج الموظف الحالي
export const logoutEmployee = (): void => {
  removeStorageItem(STORAGE_KEYS.CURRENT_EMPLOYEE);
};

// الحصول على الموظف الحالي
export const getCurrentEmployee = (): Employee | null => {
  const storedEmployee = getStorageItem(STORAGE_KEYS.CURRENT_EMPLOYEE);
  if (storedEmployee) {
    return JSON.parse(storedEmployee);
  }
  return null;
};

// تعيين الموظف الحالي
export const setCurrentEmployee = (employee: Employee): void => {
  setStorageItem(STORAGE_KEYS.CURRENT_EMPLOYEE, JSON.stringify(employee));
};

// التحقق من صلاحيات الموظف
export const hasPermission = (
  employee: Employee | null,
  section: string,
  action: string = "view"
): boolean => {
  if (!employee) {
    return false;
  }

  // المدير لديه جميع الصلاحيات
  if (employee.role === "admin") {
    return true;
  }

  // تحويل بعض أسماء الأقسام الخاصة
  const sectionMapping: Record<string, string> = {
    "shipping-settings": "shipping", // إعدادات الشحن تندرج تحت قسم الشحن
    "delivery": "shipping", // التوصيل يندرج تحت قسم الشحن
    "archive": "orders", // الأرشيف يندرج تحت قسم الطلبات

  };

  // استخدام القسم المعدل إذا كان موجودًا في التعيين
  const mappedSection = sectionMapping[section] || section;

  // التحقق من وجود الصلاحيات
  if (employee.permissions) {
    // البحث عن القسم في صلاحيات الموظف
    const sectionPermission = employee.permissions.find(
      (permission) => permission.section === mappedSection
    );

    // التحقق من وجود الإجراء في صلاحيات القسم
    if (sectionPermission) {
      return sectionPermission.actions.includes(action as any);
    }
  }

  // التحقق من القسم في accessibleSections (للتوافق مع الإصدارات القديمة)
  if (employee.accessibleSections) {
    return employee.accessibleSections.includes(mappedSection as SystemSection);
  }

  return false;
};
