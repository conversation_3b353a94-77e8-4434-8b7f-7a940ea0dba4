const e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,t=Object.keys,n=Array.isArray;function r(e,n){return"object"!=typeof n||t(n).forEach((function(t){e[t]=n[t]})),e}"undefined"==typeof Promise||e.Promise||(e.Promise=Promise);const s=Object.getPrototypeOf,i={}.hasOwnProperty;function o(e,t){return i.call(e,t)}function a(e,n){"function"==typeof n&&(n=n(s(e))),("undefined"==typeof Reflect?t:Reflect.ownKeys)(n).forEach((t=>{l(e,t,n[t])}))}const u=Object.defineProperty;function l(e,t,n,s){u(e,t,r(n&&o(n,"get")&&"function"==typeof n.get?{get:n.get,set:n.set,configurable:!0}:{value:n,configurable:!0,writable:!0},s))}function c(e){return{from:function(t){return e.prototype=Object.create(t.prototype),l(e.prototype,"constructor",e),{extend:a.bind(null,e.prototype)}}}}const h=Object.getOwnPropertyDescriptor;function d(e,t){let n;return h(e,t)||(n=s(e))&&d(n,t)}const f=[].slice;function p(e,t,n){return f.call(e,t,n)}function y(e,t){return t(e)}function m(e){if(!e)throw new Error("Assertion Failed")}function b(t){e.setImmediate?setImmediate(t):setTimeout(t,0)}function g(e,t){if("string"==typeof t&&o(e,t))return e[t];if(!t)return e;if("string"!=typeof t){for(var n=[],r=0,s=t.length;r<s;++r){var i=g(e,t[r]);n.push(i)}return n}var a=t.indexOf(".");if(-1!==a){var u=e[t.substr(0,a)];return null==u?void 0:g(u,t.substr(a+1))}}function v(e,t,r){if(e&&void 0!==t&&(!("isFrozen"in Object)||!Object.isFrozen(e)))if("string"!=typeof t&&"length"in t){m("string"!=typeof r&&"length"in r);for(var s=0,i=t.length;s<i;++s)v(e,t[s],r[s])}else{var a=t.indexOf(".");if(-1!==a){var u=t.substr(0,a),l=t.substr(a+1);if(""===l)void 0===r?n(e)&&!isNaN(parseInt(u))?e.splice(u,1):delete e[u]:e[u]=r;else{var c=e[u];c&&o(e,u)||(c=e[u]={}),v(c,l,r)}}else void 0===r?n(e)&&!isNaN(parseInt(t))?e.splice(t,1):delete e[t]:e[t]=r}}function w(e){var t={};for(var n in e)o(e,n)&&(t[n]=e[n]);return t}const _=[].concat;function x(e){return _.apply([],e)}const k="BigUint64Array,BigInt64Array,Array,Boolean,String,Date,RegExp,Blob,File,FileList,FileSystemFileHandle,FileSystemDirectoryHandle,ArrayBuffer,DataView,Uint8ClampedArray,ImageBitmap,ImageData,Map,Set,CryptoKey".split(",").concat(x([8,16,32,64].map((e=>["Int","Uint","Float"].map((t=>t+e+"Array")))))).filter((t=>e[t])),O=new Set(k.map((t=>e[t])));function P(e){const t={};for(const n in e)if(o(e,n)){const r=e[n];t[n]=!r||"object"!=typeof r||O.has(r.constructor)?r:P(r)}return t}let K=null;function E(e){K=new WeakMap;const t=S(e);return K=null,t}function S(e){if(!e||"object"!=typeof e)return e;let t=K.get(e);if(t)return t;if(n(e)){t=[],K.set(e,t);for(var r=0,i=e.length;r<i;++r)t.push(S(e[r]))}else if(O.has(e.constructor))t=e;else{const n=s(e);for(var a in t=n===Object.prototype?{}:Object.create(n),K.set(e,t),e)o(e,a)&&(t[a]=S(e[a]))}return t}const{toString:A}={};function C(e){return A.call(e).slice(8,-1)}const j="undefined"!=typeof Symbol?Symbol.iterator:"@@iterator",q="symbol"==typeof j?function(e){var t;return null!=e&&(t=e[j])&&t.apply(e)}:function(){return null};function D(e,t){const n=e.indexOf(t);return n>=0&&e.splice(n,1),n>=0}const T={};function I(e){var t,r,s,i;if(1===arguments.length){if(n(e))return e.slice();if(this===T&&"string"==typeof e)return[e];if(i=q(e)){for(r=[];!(s=i.next()).done;)r.push(s.value);return r}if(null==e)return[e];if("number"==typeof(t=e.length)){for(r=new Array(t);t--;)r[t]=e[t];return r}return[e]}for(t=arguments.length,r=new Array(t);t--;)r[t]=arguments[t];return r}const B="undefined"!=typeof Symbol?e=>"AsyncFunction"===e[Symbol.toStringTag]:()=>!1;var R=["Unknown","Constraint","Data","TransactionInactive","ReadOnly","Version","NotFound","InvalidState","InvalidAccess","Abort","Timeout","QuotaExceeded","Syntax","DataClone"],$=["Modify","Bulk","OpenFailed","VersionChange","Schema","Upgrade","InvalidTable","MissingAPI","NoSuchDatabase","InvalidArgument","SubTransaction","Unsupported","Internal","DatabaseClosed","PrematureCommit","ForeignAwait"].concat(R),N={VersionChanged:"Database version changed by other database connection",DatabaseClosed:"Database has been closed",Abort:"Transaction aborted",TransactionInactive:"Transaction has already completed or failed",MissingAPI:"IndexedDB API missing. Please visit https://tinyurl.com/y2uuvskb"};function F(e,t){this.name=e,this.message=t}function M(e,t){return e+". Errors: "+Object.keys(t).map((e=>t[e].toString())).filter(((e,t,n)=>n.indexOf(e)===t)).join("\n")}function L(e,t,n,r){this.failures=t,this.failedKeys=r,this.successCount=n,this.message=M(e,t)}function U(e,t){this.name="BulkError",this.failures=Object.keys(t).map((e=>t[e])),this.failuresByPos=t,this.message=M(e,this.failures)}c(F).from(Error).extend({toString:function(){return this.name+": "+this.message}}),c(L).from(F),c(U).from(F);var V=$.reduce(((e,t)=>(e[t]=t+"Error",e)),{});const z=F;var W=$.reduce(((e,t)=>{var n=t+"Error";function r(e,r){this.name=n,e?"string"==typeof e?(this.message=`${e}${r?"\n "+r:""}`,this.inner=r||null):"object"==typeof e&&(this.message=`${e.name} ${e.message}`,this.inner=e):(this.message=N[t]||n,this.inner=null)}return c(r).from(z),e[t]=r,e}),{});W.Syntax=SyntaxError,W.Type=TypeError,W.Range=RangeError;var Y=R.reduce(((e,t)=>(e[t+"Error"]=W[t],e)),{});var G=$.reduce(((e,t)=>(-1===["Syntax","Type","Range"].indexOf(t)&&(e[t+"Error"]=W[t]),e)),{});function Q(){}function X(e){return e}function H(e,t){return null==e||e===X?t:function(n){return t(e(n))}}function J(e,t){return function(){e.apply(this,arguments),t.apply(this,arguments)}}function Z(e,t){return e===Q?t:function(){var n=e.apply(this,arguments);void 0!==n&&(arguments[0]=n);var r=this.onsuccess,s=this.onerror;this.onsuccess=null,this.onerror=null;var i=t.apply(this,arguments);return r&&(this.onsuccess=this.onsuccess?J(r,this.onsuccess):r),s&&(this.onerror=this.onerror?J(s,this.onerror):s),void 0!==i?i:n}}function ee(e,t){return e===Q?t:function(){e.apply(this,arguments);var n=this.onsuccess,r=this.onerror;this.onsuccess=this.onerror=null,t.apply(this,arguments),n&&(this.onsuccess=this.onsuccess?J(n,this.onsuccess):n),r&&(this.onerror=this.onerror?J(r,this.onerror):r)}}function te(e,t){return e===Q?t:function(n){var s=e.apply(this,arguments);r(n,s);var i=this.onsuccess,o=this.onerror;this.onsuccess=null,this.onerror=null;var a=t.apply(this,arguments);return i&&(this.onsuccess=this.onsuccess?J(i,this.onsuccess):i),o&&(this.onerror=this.onerror?J(o,this.onerror):o),void 0===s?void 0===a?void 0:a:r(s,a)}}function ne(e,t){return e===Q?t:function(){return!1!==t.apply(this,arguments)&&e.apply(this,arguments)}}function re(e,t){return e===Q?t:function(){var n=e.apply(this,arguments);if(n&&"function"==typeof n.then){for(var r=this,s=arguments.length,i=new Array(s);s--;)i[s]=arguments[s];return n.then((function(){return t.apply(r,i)}))}return t.apply(this,arguments)}}G.ModifyError=L,G.DexieError=F,G.BulkError=U;var se="undefined"!=typeof location&&/^(http|https):\/\/(localhost|127\.0\.0\.1)/.test(location.href);function ie(e,t){se=e}var oe={};const[ae,ue,le]="undefined"==typeof Promise?[]:(()=>{let e=Promise.resolve();if("undefined"==typeof crypto||!crypto.subtle)return[e,s(e),e];const t=crypto.subtle.digest("SHA-512",new Uint8Array([0]));return[t,s(t),e]})(),ce=ue&&ue.then,he=ae&&ae.constructor,de=!!le;var fe=function(e,t){_e.push([e,t]),ye&&(queueMicrotask(qe),ye=!1)},pe=!0,ye=!0,me=[],be=[],ge=X,ve={id:"global",global:!0,ref:0,unhandleds:[],onunhandled:Q,pgp:!1,env:{},finalize:Q},we=ve,_e=[],xe=0,ke=[];function Oe(e){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");this._listeners=[],this._lib=!1;var t=this._PSD=we;if("function"!=typeof e){if(e!==oe)throw new TypeError("Not a function");return this._state=arguments[1],this._value=arguments[2],void(!1===this._state&&Se(this,this._value))}this._state=null,this._value=null,++t.ref,Ee(this,e)}const Pe={get:function(){var e=we,t=Le;function n(n,r){var s=!e.global&&(e!==we||t!==Le);const i=s&&!We();var o=new Oe(((t,o)=>{Ce(this,new Ke(Ze(n,e,s,i),Ze(r,e,s,i),t,o,e))}));return this._consoleTask&&(o._consoleTask=this._consoleTask),o}return n.prototype=oe,n},set:function(e){l(this,"then",e&&e.prototype===oe?Pe:{get:function(){return e},set:Pe.set})}};function Ke(e,t,n,r,s){this.onFulfilled="function"==typeof e?e:null,this.onRejected="function"==typeof t?t:null,this.resolve=n,this.reject=r,this.psd=s}function Ee(e,t){try{t((t=>{if(null===e._state){if(t===e)throw new TypeError("A promise cannot be resolved with itself.");var n=e._lib&&De();t&&"function"==typeof t.then?Ee(e,((e,n)=>{t instanceof Oe?t._then(e,n):t.then(e,n)})):(e._state=!0,e._value=t,Ae(e)),n&&Te()}}),Se.bind(null,e))}catch(t){Se(e,t)}}function Se(e,t){if(be.push(t),null===e._state){var n=e._lib&&De();t=ge(t),e._state=!1,e._value=t,function(e){me.some((t=>t._value===e._value))||me.push(e)}(e),Ae(e),n&&Te()}}function Ae(e){var t=e._listeners;e._listeners=[];for(var n=0,r=t.length;n<r;++n)Ce(e,t[n]);var s=e._PSD;--s.ref||s.finalize(),0===xe&&(++xe,fe((()=>{0==--xe&&Ie()}),[]))}function Ce(e,t){if(null!==e._state){var n=e._state?t.onFulfilled:t.onRejected;if(null===n)return(e._state?t.resolve:t.reject)(e._value);++t.psd.ref,++xe,fe(je,[n,e,t])}else e._listeners.push(t)}function je(e,t,n){try{var r,s=t._value;!t._state&&be.length&&(be=[]),r=se&&t._consoleTask?t._consoleTask.run((()=>e(s))):e(s),t._state||-1!==be.indexOf(s)||function(e){var t=me.length;for(;t;)if(me[--t]._value===e._value)return void me.splice(t,1)}(t),n.resolve(r)}catch(e){n.reject(e)}finally{0==--xe&&Ie(),--n.psd.ref||n.psd.finalize()}}function qe(){Je(ve,(()=>{De()&&Te()}))}function De(){var e=pe;return pe=!1,ye=!1,e}function Te(){var e,t,n;do{for(;_e.length>0;)for(e=_e,_e=[],n=e.length,t=0;t<n;++t){var r=e[t];r[0].apply(null,r[1])}}while(_e.length>0);pe=!0,ye=!0}function Ie(){var e=me;me=[],e.forEach((e=>{e._PSD.onunhandled.call(null,e._value,e)}));for(var t=ke.slice(0),n=t.length;n;)t[--n]()}function Be(e){return new Oe(oe,!1,e)}function Re(e,t){var n=we;return function(){var r=De(),s=we;try{return Xe(n,!0),e.apply(this,arguments)}catch(e){t&&t(e)}finally{Xe(s,!1),r&&Te()}}}a(Oe.prototype,{then:Pe,_then:function(e,t){Ce(this,new Ke(null,null,e,t,we))},catch:function(e){if(1===arguments.length)return this.then(null,e);var t=arguments[0],n=arguments[1];return"function"==typeof t?this.then(null,(e=>e instanceof t?n(e):Be(e))):this.then(null,(e=>e&&e.name===t?n(e):Be(e)))},finally:function(e){return this.then((t=>Oe.resolve(e()).then((()=>t))),(t=>Oe.resolve(e()).then((()=>Be(t)))))},timeout:function(e,t){return e<1/0?new Oe(((n,r)=>{var s=setTimeout((()=>r(new W.Timeout(t))),e);this.then(n,r).finally(clearTimeout.bind(null,s))})):this}}),"undefined"!=typeof Symbol&&Symbol.toStringTag&&l(Oe.prototype,Symbol.toStringTag,"Dexie.Promise"),ve.env=He(),a(Oe,{all:function(){var e=I.apply(null,arguments).map(Ye);return new Oe((function(t,n){0===e.length&&t([]);var r=e.length;e.forEach(((s,i)=>Oe.resolve(s).then((n=>{e[i]=n,--r||t(e)}),n)))}))},resolve:e=>e instanceof Oe?e:e&&"function"==typeof e.then?new Oe(((t,n)=>{e.then(t,n)})):new Oe(oe,!0,e),reject:Be,race:function(){var e=I.apply(null,arguments).map(Ye);return new Oe(((t,n)=>{e.map((e=>Oe.resolve(e).then(t,n)))}))},PSD:{get:()=>we,set:e=>we=e},totalEchoes:{get:()=>Le},newPSD:Ve,usePSD:Je,scheduler:{get:()=>fe,set:e=>{fe=e}},rejectionMapper:{get:()=>ge,set:e=>{ge=e}},follow:(e,t)=>new Oe(((n,r)=>Ve(((t,n)=>{var r=we;r.unhandleds=[],r.onunhandled=n,r.finalize=J((function(){!function(e){function t(){e(),ke.splice(ke.indexOf(t),1)}ke.push(t),++xe,fe((()=>{0==--xe&&Ie()}),[])}((()=>{0===this.unhandleds.length?t():n(this.unhandleds[0])}))}),r.finalize),e()}),t,n,r)))}),he&&(he.allSettled&&l(Oe,"allSettled",(function(){const e=I.apply(null,arguments).map(Ye);return new Oe((t=>{0===e.length&&t([]);let n=e.length;const r=new Array(n);e.forEach(((e,s)=>Oe.resolve(e).then((e=>r[s]={status:"fulfilled",value:e}),(e=>r[s]={status:"rejected",reason:e})).then((()=>--n||t(r)))))}))})),he.any&&"undefined"!=typeof AggregateError&&l(Oe,"any",(function(){const e=I.apply(null,arguments).map(Ye);return new Oe(((t,n)=>{0===e.length&&n(new AggregateError([]));let r=e.length;const s=new Array(r);e.forEach(((e,i)=>Oe.resolve(e).then((e=>t(e)),(e=>{s[i]=e,--r||n(new AggregateError(s))}))))}))})),he.withResolvers&&(Oe.withResolvers=he.withResolvers));const $e={awaits:0,echoes:0,id:0};var Ne=0,Fe=[],Me=0,Le=0,Ue=0;function Ve(e,t,n,s){var i=we,o=Object.create(i);o.parent=i,o.ref=0,o.global=!1,o.id=++Ue,ve.env,o.env=de?{Promise:Oe,PromiseProp:{value:Oe,configurable:!0,writable:!0},all:Oe.all,race:Oe.race,allSettled:Oe.allSettled,any:Oe.any,resolve:Oe.resolve,reject:Oe.reject}:{},t&&r(o,t),++i.ref,o.finalize=function(){--this.parent.ref||this.parent.finalize()};var a=Je(o,e,n,s);return 0===o.ref&&o.finalize(),a}function ze(){return $e.id||($e.id=++Ne),++$e.awaits,$e.echoes+=100,$e.id}function We(){return!!$e.awaits&&(0==--$e.awaits&&($e.id=0),$e.echoes=100*$e.awaits,!0)}function Ye(e){return $e.echoes&&e&&e.constructor===he?(ze(),e.then((e=>(We(),e)),(e=>(We(),tt(e))))):e}function Ge(e){++Le,$e.echoes&&0!=--$e.echoes||($e.echoes=$e.awaits=$e.id=0),Fe.push(we),Xe(e,!0)}function Qe(){var e=Fe[Fe.length-1];Fe.pop(),Xe(e,!1)}function Xe(t,n){var r=we;if((n?!$e.echoes||Me++&&t===we:!Me||--Me&&t===we)||queueMicrotask(n?Ge.bind(null,t):Qe),t!==we&&(we=t,r===ve&&(ve.env=He()),de)){var s=ve.env.Promise,i=t.env;(r.global||t.global)&&(Object.defineProperty(e,"Promise",i.PromiseProp),s.all=i.all,s.race=i.race,s.resolve=i.resolve,s.reject=i.reject,i.allSettled&&(s.allSettled=i.allSettled),i.any&&(s.any=i.any))}}function He(){var t=e.Promise;return de?{Promise:t,PromiseProp:Object.getOwnPropertyDescriptor(e,"Promise"),all:t.all,race:t.race,allSettled:t.allSettled,any:t.any,resolve:t.resolve,reject:t.reject}:{}}function Je(e,t,n,r,s){var i=we;try{return Xe(e,!0),t(n,r,s)}finally{Xe(i,!1)}}function Ze(e,t,n,r){return"function"!=typeof e?e:function(){var s=we;n&&ze(),Xe(t,!0);try{return e.apply(this,arguments)}finally{Xe(s,!1),r&&queueMicrotask(We)}}}function et(e){Promise===he&&0===$e.echoes?0===Me?e():enqueueNativeMicroTask(e):setTimeout(e,0)}-1===(""+ce).indexOf("[native code]")&&(ze=We=Q);var tt=Oe.reject;function nt(e,t,n,r){if(e.idbdb&&(e._state.openComplete||we.letThrough||e._vip)){var s=e._createTransaction(t,n,e._dbSchema);try{s.create(),e._state.PR1398_maxLoop=3}catch(s){return s.name===V.InvalidState&&e.isOpen()&&--e._state.PR1398_maxLoop>0?(console.warn("Dexie: Need to reopen db"),e.close({disableAutoOpen:!1}),e.open().then((()=>nt(e,t,n,r)))):tt(s)}return s._promise(t,((e,t)=>Ve((()=>(we.trans=s,r(e,t,s)))))).then((e=>{if("readwrite"===t)try{s.idbtrans.commit()}catch{}return"readonly"===t?e:s._completion.then((()=>e))}))}if(e._state.openComplete)return tt(new W.DatabaseClosed(e._state.dbOpenError));if(!e._state.isBeingOpened){if(!e._state.autoOpen)return tt(new W.DatabaseClosed);e.open().catch(Q)}return e._state.dbReadyPromise.then((()=>nt(e,t,n,r)))}const rt=String.fromCharCode(65535),st="Invalid key provided. Keys must be of type string, number, Date or Array<string | number | Date>.",it=[];function ot(e,t){return e?t?function(){return e.apply(this,arguments)&&t.apply(this,arguments)}:e:t}const at={type:3,lower:-1/0,lowerOpen:!1,upper:[[]],upperOpen:!1};function ut(e){return"string"!=typeof e||/\./.test(e)?e=>e:t=>(void 0===t[e]&&e in t&&delete(t=E(t))[e],t)}function lt(){throw W.Type()}function ct(e,t){try{const n=ht(e),r=ht(t);if(n!==r)return"Array"===n?1:"Array"===r?-1:"binary"===n?1:"binary"===r?-1:"string"===n?1:"string"===r?-1:"Date"===n?1:"Date"!==r?NaN:-1;switch(n){case"number":case"Date":case"string":return e>t?1:e<t?-1:0;case"binary":return function(e,t){const n=e.length,r=t.length,s=n<r?n:r;for(let n=0;n<s;++n)if(e[n]!==t[n])return e[n]<t[n]?-1:1;return n===r?0:n<r?-1:1}(dt(e),dt(t));case"Array":return function(e,t){const n=e.length,r=t.length,s=n<r?n:r;for(let n=0;n<s;++n){const r=ct(e[n],t[n]);if(0!==r)return r}return n===r?0:n<r?-1:1}(e,t)}}catch{}return NaN}function ht(e){const t=typeof e;if("object"!==t)return t;if(ArrayBuffer.isView(e))return"binary";const n=C(e);return"ArrayBuffer"===n?"binary":n}function dt(e){return e instanceof Uint8Array?e:ArrayBuffer.isView(e)?new Uint8Array(e.buffer,e.byteOffset,e.byteLength):new Uint8Array(e)}class ft{_trans(e,t,n){const r=this._tx||we.trans,s=this.name,i=se&&"undefined"!=typeof console&&console.createTask&&console.createTask(`Dexie: ${"readonly"===e?"read":"write"} ${this.name}`);function o(e,n,r){if(!r.schema[s])throw new W.NotFound("Table "+s+" not part of transaction");return t(r.idbtrans,r)}const a=De();try{let t=r&&r.db._novip===this.db._novip?r===we.trans?r._promise(e,o,n):Ve((()=>r._promise(e,o,n)),{trans:r,transless:we.transless||we}):nt(this.db,e,[this.name],o);return i&&(t._consoleTask=i,t=t.catch((e=>(console.trace(e),tt(e))))),t}finally{a&&Te()}}get(e,t){return e&&e.constructor===Object?this.where(e).first(t):null==e?tt(new W.Type("Invalid argument to Table.get()")):this._trans("readonly",(t=>this.core.get({trans:t,key:e}).then((e=>this.hook.reading.fire(e))))).then(t)}where(e){if("string"==typeof e)return new this.db.WhereClause(this,e);if(n(e))return new this.db.WhereClause(this,`[${e.join("+")}]`);const r=t(e);if(1===r.length)return this.where(r[0]).equals(e[r[0]]);const s=this.schema.indexes.concat(this.schema.primKey).filter((e=>{if(e.compound&&r.every((t=>e.keyPath.indexOf(t)>=0))){for(let t=0;t<r.length;++t)if(-1===r.indexOf(e.keyPath[t]))return!1;return!0}return!1})).sort(((e,t)=>e.keyPath.length-t.keyPath.length))[0];if(s&&this.db._maxKey!==rt){const t=s.keyPath.slice(0,r.length);return this.where(t).equals(t.map((t=>e[t])))}!s&&se&&console.warn(`The query ${JSON.stringify(e)} on ${this.name} would benefit from a compound index [${r.join("+")}]`);const{idxByName:i}=this.schema;function o(e,t){return 0===ct(e,t)}const[a,u]=r.reduce((([t,r],s)=>{const a=i[s],u=e[s];return[t||a,t||!a?ot(r,a&&a.multi?e=>{const t=g(e,s);return n(t)&&t.some((e=>o(u,e)))}:e=>o(u,g(e,s))):r]}),[null,null]);return a?this.where(a.name).equals(e[a.keyPath]).filter(u):s?this.filter(u):this.where(r).equals("")}filter(e){return this.toCollection().and(e)}count(e){return this.toCollection().count(e)}offset(e){return this.toCollection().offset(e)}limit(e){return this.toCollection().limit(e)}each(e){return this.toCollection().each(e)}toArray(e){return this.toCollection().toArray(e)}toCollection(){return new this.db.Collection(new this.db.WhereClause(this))}orderBy(e){return new this.db.Collection(new this.db.WhereClause(this,n(e)?`[${e.join("+")}]`:e))}reverse(){return this.toCollection().reverse()}mapToClass(e){const{db:t,name:n}=this;this.schema.mappedClass=e,e.prototype instanceof lt&&(e=class extends e{get db(){return t}table(){return n}});const r=new Set;for(let t=e.prototype;t;t=s(t))Object.getOwnPropertyNames(t).forEach((e=>r.add(e)));const i=t=>{if(!t)return t;const n=Object.create(e.prototype);for(let e in t)if(!r.has(e))try{n[e]=t[e]}catch(e){}return n};return this.schema.readHook&&this.hook.reading.unsubscribe(this.schema.readHook),this.schema.readHook=i,this.hook("reading",i),e}defineClass(){return this.mapToClass((function(e){r(this,e)}))}add(e,t){const{auto:n,keyPath:r}=this.schema.primKey;let s=e;return r&&n&&(s=ut(r)(e)),this._trans("readwrite",(e=>this.core.mutate({trans:e,type:"add",keys:null!=t?[t]:null,values:[s]}))).then((e=>e.numFailures?Oe.reject(e.failures[0]):e.lastResult)).then((t=>{if(r)try{v(e,r,t)}catch(e){}return t}))}update(e,t){if("object"!=typeof e||n(e))return this.where(":id").equals(e).modify(t);{const n=g(e,this.schema.primKey.keyPath);return void 0===n?tt(new W.InvalidArgument("Given object does not contain its primary key")):this.where(":id").equals(n).modify(t)}}put(e,t){const{auto:n,keyPath:r}=this.schema.primKey;let s=e;return r&&n&&(s=ut(r)(e)),this._trans("readwrite",(e=>this.core.mutate({trans:e,type:"put",values:[s],keys:null!=t?[t]:null}))).then((e=>e.numFailures?Oe.reject(e.failures[0]):e.lastResult)).then((t=>{if(r)try{v(e,r,t)}catch(e){}return t}))}delete(e){return this._trans("readwrite",(t=>this.core.mutate({trans:t,type:"delete",keys:[e]}))).then((e=>e.numFailures?Oe.reject(e.failures[0]):void 0))}clear(){return this._trans("readwrite",(e=>this.core.mutate({trans:e,type:"deleteRange",range:at}))).then((e=>e.numFailures?Oe.reject(e.failures[0]):void 0))}bulkGet(e){return this._trans("readonly",(t=>this.core.getMany({keys:e,trans:t}).then((e=>e.map((e=>this.hook.reading.fire(e)))))))}bulkAdd(e,t,n){const r=Array.isArray(t)?t:void 0,s=(n=n||(r?void 0:t))?n.allKeys:void 0;return this._trans("readwrite",(t=>{const{auto:n,keyPath:i}=this.schema.primKey;if(i&&r)throw new W.InvalidArgument("bulkAdd(): keys argument invalid on tables with inbound keys");if(r&&r.length!==e.length)throw new W.InvalidArgument("Arguments objects and keys must have the same length");const o=e.length;let a=i&&n?e.map(ut(i)):e;return this.core.mutate({trans:t,type:"add",keys:r,values:a,wantResults:s}).then((({numFailures:e,results:t,lastResult:n,failures:r})=>{if(0===e)return s?t:n;throw new U(`${this.name}.bulkAdd(): ${e} of ${o} operations failed`,r)}))}))}bulkPut(e,t,n){const r=Array.isArray(t)?t:void 0,s=(n=n||(r?void 0:t))?n.allKeys:void 0;return this._trans("readwrite",(t=>{const{auto:n,keyPath:i}=this.schema.primKey;if(i&&r)throw new W.InvalidArgument("bulkPut(): keys argument invalid on tables with inbound keys");if(r&&r.length!==e.length)throw new W.InvalidArgument("Arguments objects and keys must have the same length");const o=e.length;let a=i&&n?e.map(ut(i)):e;return this.core.mutate({trans:t,type:"put",keys:r,values:a,wantResults:s}).then((({numFailures:e,results:t,lastResult:n,failures:r})=>{if(0===e)return s?t:n;throw new U(`${this.name}.bulkPut(): ${e} of ${o} operations failed`,r)}))}))}bulkUpdate(e){const t=this.core,n=e.map((e=>e.key)),r=e.map((e=>e.changes)),s=[];return this._trans("readwrite",(i=>t.getMany({trans:i,keys:n,cache:"clone"}).then((o=>{const a=[],u=[];e.forEach((({key:e,changes:t},n)=>{const r=o[n];if(r){for(const n of Object.keys(t)){const s=t[n];if(n===this.schema.primKey.keyPath){if(0!==ct(s,e))throw new W.Constraint("Cannot update primary key in bulkUpdate()")}else v(r,n,s)}s.push(n),a.push(e),u.push(r)}}));const l=a.length;return t.mutate({trans:i,type:"put",keys:a,values:u,updates:{keys:n,changeSpecs:r}}).then((({numFailures:e,failures:t})=>{if(0===e)return l;for(const e of Object.keys(t)){const n=s[Number(e)];if(null!=n){const r=t[e];delete t[e],t[n]=r}}throw new U(`${this.name}.bulkUpdate(): ${e} of ${l} operations failed`,t)}))}))))}bulkDelete(e){const t=e.length;return this._trans("readwrite",(t=>this.core.mutate({trans:t,type:"delete",keys:e}))).then((({numFailures:e,lastResult:n,failures:r})=>{if(0===e)return n;throw new U(`${this.name}.bulkDelete(): ${e} of ${t} operations failed`,r)}))}}function pt(e){var r={},s=function(t,n){if(n){for(var s=arguments.length,i=new Array(s-1);--s;)i[s-1]=arguments[s];return r[t].subscribe.apply(null,i),e}if("string"==typeof t)return r[t]};s.addEventType=a;for(var i=1,o=arguments.length;i<o;++i)a(arguments[i]);return s;function a(e,t,n){if("object"==typeof e)return u(e);t||(t=ne),n||(n=Q);var i={subscribers:[],fire:n,subscribe:function(e){-1===i.subscribers.indexOf(e)&&(i.subscribers.push(e),i.fire=t(i.fire,e))},unsubscribe:function(e){i.subscribers=i.subscribers.filter((function(t){return t!==e})),i.fire=i.subscribers.reduce(t,n)}};return r[e]=s[e]=i,i}function u(e){t(e).forEach((function(t){var r=e[t];if(n(r))a(t,e[t][0],e[t][1]);else{if("asap"!==r)throw new W.InvalidArgument("Invalid event config");var s=a(t,X,(function(){for(var e=arguments.length,t=new Array(e);e--;)t[e]=arguments[e];s.subscribers.forEach((function(e){b((function(){e.apply(null,t)}))}))}))}}))}}function yt(e,t){return c(t).from({prototype:e}),t}function mt(e,t){return!(e.filter||e.algorithm||e.or)&&(t?e.justLimit:!e.replayFilter)}function bt(e,t){e.filter=ot(e.filter,t)}function gt(e,t,n){var r=e.replayFilter;e.replayFilter=r?()=>ot(r(),t()):t,e.justLimit=n&&!r}function vt(e,t){if(e.isPrimKey)return t.primaryKey;const n=t.getIndexByKeyPath(e.index);if(!n)throw new W.Schema("KeyPath "+e.index+" on object store "+t.name+" is not indexed");return n}function wt(e,t,n){const r=vt(e,t.schema);return t.openCursor({trans:n,values:!e.keysOnly,reverse:"prev"===e.dir,unique:!!e.unique,query:{index:r,range:e.range}})}function _t(e,t,n,r){const s=e.replayFilter?ot(e.filter,e.replayFilter()):e.filter;if(e.or){const i={},a=(e,n,r)=>{if(!s||s(n,r,(e=>n.stop(e)),(e=>n.fail(e)))){var a=n.primaryKey,u=""+a;"[object ArrayBuffer]"===u&&(u=""+new Uint8Array(a)),o(i,u)||(i[u]=!0,t(e,n,r))}};return Promise.all([e.or._iterate(a,n),xt(wt(e,r,n),e.algorithm,a,!e.keysOnly&&e.valueMapper)])}return xt(wt(e,r,n),ot(e.algorithm,s),t,!e.keysOnly&&e.valueMapper)}function xt(e,t,n,r){var s=Re(r?(e,t,s)=>n(r(e),t,s):n);return e.then((e=>{if(e)return e.start((()=>{var n=()=>e.continue();t&&!t(e,(e=>n=e),(t=>{e.stop(t),n=Q}),(t=>{e.fail(t),n=Q}))||s(e.value,e,(e=>n=e)),n()}))}))}class kt{execute(e){const t=this["@@propmod"];if(void 0!==t.add){const r=t.add;if(n(r))return[...n(e)?e:[],...r].sort();if("number"==typeof r)return(Number(e)||0)+r;if("bigint"==typeof r)try{return BigInt(e)+r}catch{return BigInt(0)+r}throw new TypeError(`Invalid term ${r}`)}if(void 0!==t.remove){const r=t.remove;if(n(r))return n(e)?e.filter((e=>!r.includes(e))).sort():[];if("number"==typeof r)return Number(e)-r;if("bigint"==typeof r)try{return BigInt(e)-r}catch{return BigInt(0)-r}throw new TypeError(`Invalid subtrahend ${r}`)}const r=t.replacePrefix?.[0];return r&&"string"==typeof e&&e.startsWith(r)?t.replacePrefix[1]+e.substring(r.length):e}constructor(e){this["@@propmod"]=e}}class Ot{_read(e,t){var n=this._ctx;return n.error?n.table._trans(null,tt.bind(null,n.error)):n.table._trans("readonly",e).then(t)}_write(e){var t=this._ctx;return t.error?t.table._trans(null,tt.bind(null,t.error)):t.table._trans("readwrite",e,"locked")}_addAlgorithm(e){var t=this._ctx;t.algorithm=ot(t.algorithm,e)}_iterate(e,t){return _t(this._ctx,e,t,this._ctx.table.core)}clone(e){var t=Object.create(this.constructor.prototype),n=Object.create(this._ctx);return e&&r(n,e),t._ctx=n,t}raw(){return this._ctx.valueMapper=null,this}each(e){var t=this._ctx;return this._read((n=>_t(t,e,n,t.table.core)))}count(e){return this._read((e=>{const t=this._ctx,n=t.table.core;if(mt(t,!0))return n.count({trans:e,query:{index:vt(t,n.schema),range:t.range}}).then((e=>Math.min(e,t.limit)));var r=0;return _t(t,(()=>(++r,!1)),e,n).then((()=>r))})).then(e)}sortBy(e,t){const n=e.split(".").reverse(),r=n[0],s=n.length-1;function i(e,t){return t?i(e[n[t]],t-1):e[r]}var o="next"===this._ctx.dir?1:-1;function a(e,t){return ct(i(e,s),i(t,s))*o}return this.toArray((function(e){return e.sort(a)})).then(t)}toArray(e){return this._read((e=>{var t=this._ctx;if("next"===t.dir&&mt(t,!0)&&t.limit>0){const{valueMapper:n}=t,r=vt(t,t.table.core.schema);return t.table.core.query({trans:e,limit:t.limit,values:!0,query:{index:r,range:t.range}}).then((({result:e})=>n?e.map(n):e))}{const n=[];return _t(t,(e=>n.push(e)),e,t.table.core).then((()=>n))}}),e)}offset(e){var t=this._ctx;return e<=0||(t.offset+=e,mt(t)?gt(t,(()=>{var t=e;return(e,n)=>0===t||(1===t?(--t,!1):(n((()=>{e.advance(t),t=0})),!1))})):gt(t,(()=>{var t=e;return()=>--t<0}))),this}limit(e){return this._ctx.limit=Math.min(this._ctx.limit,e),gt(this._ctx,(()=>{var t=e;return function(e,n,r){return--t<=0&&n(r),t>=0}}),!0),this}until(e,t){return bt(this._ctx,(function(n,r,s){return!e(n.value)||(r(s),t)})),this}first(e){return this.limit(1).toArray((function(e){return e[0]})).then(e)}last(e){return this.reverse().first(e)}filter(e){var t,n;return bt(this._ctx,(function(t){return e(t.value)})),t=this._ctx,n=e,t.isMatch=ot(t.isMatch,n),this}and(e){return this.filter(e)}or(e){return new this.db.WhereClause(this._ctx.table,e,this)}reverse(){return this._ctx.dir="prev"===this._ctx.dir?"next":"prev",this._ondirectionchange&&this._ondirectionchange(this._ctx.dir),this}desc(){return this.reverse()}eachKey(e){var t=this._ctx;return t.keysOnly=!t.isMatch,this.each((function(t,n){e(n.key,n)}))}eachUniqueKey(e){return this._ctx.unique="unique",this.eachKey(e)}eachPrimaryKey(e){var t=this._ctx;return t.keysOnly=!t.isMatch,this.each((function(t,n){e(n.primaryKey,n)}))}keys(e){var t=this._ctx;t.keysOnly=!t.isMatch;var n=[];return this.each((function(e,t){n.push(t.key)})).then((function(){return n})).then(e)}primaryKeys(e){var t=this._ctx;if("next"===t.dir&&mt(t,!0)&&t.limit>0)return this._read((e=>{var n=vt(t,t.table.core.schema);return t.table.core.query({trans:e,values:!1,limit:t.limit,query:{index:n,range:t.range}})})).then((({result:e})=>e)).then(e);t.keysOnly=!t.isMatch;var n=[];return this.each((function(e,t){n.push(t.primaryKey)})).then((function(){return n})).then(e)}uniqueKeys(e){return this._ctx.unique="unique",this.keys(e)}firstKey(e){return this.limit(1).keys((function(e){return e[0]})).then(e)}lastKey(e){return this.reverse().firstKey(e)}distinct(){var e=this._ctx,t=e.index&&e.table.schema.idxByName[e.index];if(!t||!t.multi)return this;var n={};return bt(this._ctx,(function(e){var t=e.primaryKey.toString(),r=o(n,t);return n[t]=!0,!r})),this}modify(e){var n=this._ctx;return this._write((r=>{var s;if("function"==typeof e)s=e;else{var i=t(e),o=i.length;s=function(t){let n=!1;for(let r=0;r<o;++r){let s=i[r],o=e[s],a=g(t,s);o instanceof kt?(v(t,s,o.execute(a)),n=!0):a!==o&&(v(t,s,o),n=!0)}return n}}const a=n.table.core,{outbound:u,extractKey:l}=a.schema.primaryKey;let c=200;const h=this.db._options.modifyChunkSize;h&&(c="object"==typeof h?h[a.name]||h["*"]||200:h);const d=[];let f=0;const p=[],y=(e,n)=>{const{failures:r,numFailures:s}=n;f+=e-s;for(let e of t(r))d.push(r[e])};return this.clone().primaryKeys().then((t=>{const i=mt(n)&&n.limit===1/0&&("function"!=typeof e||e===Pt)&&{index:n.index,range:n.range},o=n=>{const h=Math.min(c,t.length-n);return a.getMany({trans:r,keys:t.slice(n,n+h),cache:"immutable"}).then((d=>{const f=[],p=[],m=u?[]:null,b=[];for(let e=0;e<h;++e){const r=d[e],i={value:E(r),primKey:t[n+e]};!1!==s.call(i,i.value,i)&&(null==i.value?b.push(t[n+e]):u||0===ct(l(r),l(i.value))?(p.push(i.value),u&&m.push(t[n+e])):(b.push(t[n+e]),f.push(i.value)))}return Promise.resolve(f.length>0&&a.mutate({trans:r,type:"add",values:f}).then((e=>{for(let t in e.failures)b.splice(parseInt(t),1);y(f.length,e)}))).then((()=>(p.length>0||i&&"object"==typeof e)&&a.mutate({trans:r,type:"put",keys:m,values:p,criteria:i,changeSpec:"function"!=typeof e&&e,isAdditionalChunk:n>0}).then((e=>y(p.length,e))))).then((()=>(b.length>0||i&&e===Pt)&&a.mutate({trans:r,type:"delete",keys:b,criteria:i,isAdditionalChunk:n>0}).then((e=>y(b.length,e))))).then((()=>t.length>n+h&&o(n+c)))}))};return o(0).then((()=>{if(d.length>0)throw new L("Error modifying one or more objects",d,f,p);return t.length}))}))}))}delete(){var e=this._ctx,t=e.range;return mt(e)&&(e.isPrimKey||3===t.type)?this._write((n=>{const{primaryKey:r}=e.table.core.schema,s=t;return e.table.core.count({trans:n,query:{index:r,range:s}}).then((t=>e.table.core.mutate({trans:n,type:"deleteRange",range:s}).then((({failures:e,lastResult:n,results:r,numFailures:s})=>{if(s)throw new L("Could not delete some values",Object.keys(e).map((t=>e[t])),t-s);return t-s}))))})):this.modify(Pt)}}const Pt=(e,t)=>t.value=null;function Kt(e,t){return e<t?-1:e===t?0:1}function Et(e,t){return e>t?-1:e===t?0:1}function St(e,t,n){var r=e instanceof Tt?new e.Collection(e):e;return r._ctx.error=n?new n(t):new TypeError(t),r}function At(e){return new e.Collection(e,(()=>Dt(""))).limit(0)}function Ct(e,t,n,r,s,i){for(var o=Math.min(e.length,r.length),a=-1,u=0;u<o;++u){var l=t[u];if(l!==r[u])return s(e[u],n[u])<0?e.substr(0,u)+n[u]+n.substr(u+1):s(e[u],r[u])<0?e.substr(0,u)+r[u]+n.substr(u+1):a>=0?e.substr(0,a)+t[a]+n.substr(a+1):null;s(e[u],l)<0&&(a=u)}return o<r.length&&"next"===i?e+n.substr(e.length):o<e.length&&"prev"===i?e.substr(0,n.length):a<0?null:e.substr(0,a)+r[a]+n.substr(a+1)}function jt(e,t,n,r){var s,i,o,a,u,l,c,h=n.length;if(!n.every((e=>"string"==typeof e)))return St(e,"String expected.");function d(e){s=function(e){return"next"===e?e=>e.toUpperCase():e=>e.toLowerCase()}(e),i=function(e){return"next"===e?e=>e.toLowerCase():e=>e.toUpperCase()}(e),o="next"===e?Kt:Et;var t=n.map((function(e){return{lower:i(e),upper:s(e)}})).sort((function(e,t){return o(e.lower,t.lower)}));a=t.map((function(e){return e.upper})),u=t.map((function(e){return e.lower})),l=e,c="next"===e?"":r}d("next");var f=new e.Collection(e,(()=>qt(a[0],u[h-1]+r)));f._ondirectionchange=function(e){d(e)};var p=0;return f._addAlgorithm((function(e,n,r){var s=e.key;if("string"!=typeof s)return!1;var d=i(s);if(t(d,u,p))return!0;for(var f=null,y=p;y<h;++y){var m=Ct(s,d,a[y],u[y],o,l);null===m&&null===f?p=y+1:(null===f||o(f,m)>0)&&(f=m)}return n(null!==f?function(){e.continue(f+c)}:r),!1})),f}function qt(e,t,n,r){return{type:2,lower:e,upper:t,lowerOpen:n,upperOpen:r}}function Dt(e){return{type:1,lower:e,upper:e}}class Tt{get Collection(){return this._ctx.table.db.Collection}between(e,t,n,r){n=!1!==n,r=!0===r;try{return this._cmp(e,t)>0||0===this._cmp(e,t)&&(n||r)&&(!n||!r)?At(this):new this.Collection(this,(()=>qt(e,t,!n,!r)))}catch(e){return St(this,st)}}equals(e){return null==e?St(this,st):new this.Collection(this,(()=>Dt(e)))}above(e){return null==e?St(this,st):new this.Collection(this,(()=>qt(e,void 0,!0)))}aboveOrEqual(e){return null==e?St(this,st):new this.Collection(this,(()=>qt(e,void 0,!1)))}below(e){return null==e?St(this,st):new this.Collection(this,(()=>qt(void 0,e,!1,!0)))}belowOrEqual(e){return null==e?St(this,st):new this.Collection(this,(()=>qt(void 0,e)))}startsWith(e){return"string"!=typeof e?St(this,"String expected."):this.between(e,e+rt,!0,!0)}startsWithIgnoreCase(e){return""===e?this.startsWith(e):jt(this,((e,t)=>0===e.indexOf(t[0])),[e],rt)}equalsIgnoreCase(e){return jt(this,((e,t)=>e===t[0]),[e],"")}anyOfIgnoreCase(){var e=I.apply(T,arguments);return 0===e.length?At(this):jt(this,((e,t)=>-1!==t.indexOf(e)),e,"")}startsWithAnyOfIgnoreCase(){var e=I.apply(T,arguments);return 0===e.length?At(this):jt(this,((e,t)=>t.some((t=>0===e.indexOf(t)))),e,rt)}anyOf(){const e=I.apply(T,arguments);let t=this._cmp;try{e.sort(t)}catch(e){return St(this,st)}if(0===e.length)return At(this);const n=new this.Collection(this,(()=>qt(e[0],e[e.length-1])));n._ondirectionchange=n=>{t="next"===n?this._ascending:this._descending,e.sort(t)};let r=0;return n._addAlgorithm(((n,s,i)=>{const o=n.key;for(;t(o,e[r])>0;)if(++r,r===e.length)return s(i),!1;return 0===t(o,e[r])||(s((()=>{n.continue(e[r])})),!1)})),n}notEqual(e){return this.inAnyRange([[-(1/0),e],[e,this.db._maxKey]],{includeLowers:!1,includeUppers:!1})}noneOf(){const e=I.apply(T,arguments);if(0===e.length)return new this.Collection(this);try{e.sort(this._ascending)}catch(e){return St(this,st)}const t=e.reduce(((e,t)=>e?e.concat([[e[e.length-1][1],t]]):[[-(1/0),t]]),null);return t.push([e[e.length-1],this.db._maxKey]),this.inAnyRange(t,{includeLowers:!1,includeUppers:!1})}inAnyRange(e,t){const n=this._cmp,r=this._ascending,s=this._descending,i=this._min,o=this._max;if(0===e.length)return At(this);if(!e.every((e=>void 0!==e[0]&&void 0!==e[1]&&r(e[0],e[1])<=0)))return St(this,"First argument to inAnyRange() must be an Array of two-value Arrays [lower,upper] where upper must not be lower than lower",W.InvalidArgument);const a=!t||!1!==t.includeLowers,u=t&&!0===t.includeUppers;let l,c=r;function h(e,t){return c(e[0],t[0])}try{l=e.reduce((function(e,t){let r=0,s=e.length;for(;r<s;++r){const s=e[r];if(n(t[0],s[1])<0&&n(t[1],s[0])>0){s[0]=i(s[0],t[0]),s[1]=o(s[1],t[1]);break}}return r===s&&e.push(t),e}),[]),l.sort(h)}catch(e){return St(this,st)}let d=0;const f=u?e=>r(e,l[d][1])>0:e=>r(e,l[d][1])>=0,p=a?e=>s(e,l[d][0])>0:e=>s(e,l[d][0])>=0;let y=f;const m=new this.Collection(this,(()=>qt(l[0][0],l[l.length-1][1],!a,!u)));return m._ondirectionchange=e=>{"next"===e?(y=f,c=r):(y=p,c=s),l.sort(h)},m._addAlgorithm(((e,t,n)=>{for(var s=e.key;y(s);)if(++d,d===l.length)return t(n),!1;return!!function(e){return!f(e)&&!p(e)}(s)||(0===this._cmp(s,l[d][1])||0===this._cmp(s,l[d][0])||t((()=>{c===r?e.continue(l[d][0]):e.continue(l[d][1])})),!1)})),m}startsWithAnyOf(){const e=I.apply(T,arguments);return e.every((e=>"string"==typeof e))?0===e.length?At(this):this.inAnyRange(e.map((e=>[e,e+rt]))):St(this,"startsWithAnyOf() only works with strings")}}function It(e){return Re((function(t){return Bt(t),e(t.target.error),!1}))}function Bt(e){e.stopPropagation&&e.stopPropagation(),e.preventDefault&&e.preventDefault()}const Rt=pt(null,"storagemutated");class $t{_lock(){return m(!we.global),++this._reculock,1!==this._reculock||we.global||(we.lockOwnerFor=this),this}_unlock(){if(m(!we.global),0==--this._reculock)for(we.global||(we.lockOwnerFor=null);this._blockedFuncs.length>0&&!this._locked();){var e=this._blockedFuncs.shift();try{Je(e[1],e[0])}catch(e){}}return this}_locked(){return this._reculock&&we.lockOwnerFor!==this}create(e){if(!this.mode)return this;const t=this.db.idbdb,n=this.db._state.dbOpenError;if(m(!this.idbtrans),!e&&!t)switch(n&&n.name){case"DatabaseClosedError":throw new W.DatabaseClosed(n);case"MissingAPIError":throw new W.MissingAPI(n.message,n);default:throw new W.OpenFailed(n)}if(!this.active)throw new W.TransactionInactive;return m(null===this._completion._state),(e=this.idbtrans=e||(this.db.core?this.db.core.transaction(this.storeNames,this.mode,{durability:this.chromeTransactionDurability}):t.transaction(this.storeNames,this.mode,{durability:this.chromeTransactionDurability}))).onerror=Re((t=>{Bt(t),this._reject(e.error)})),e.onabort=Re((t=>{Bt(t),this.active&&this._reject(new W.Abort(e.error)),this.active=!1,this.on("abort").fire(t)})),e.oncomplete=Re((()=>{this.active=!1,this._resolve(),"mutatedParts"in e&&Rt.storagemutated.fire(e.mutatedParts)})),this}_promise(e,t,n){if("readwrite"===e&&"readwrite"!==this.mode)return tt(new W.ReadOnly("Transaction is readonly"));if(!this.active)return tt(new W.TransactionInactive);if(this._locked())return new Oe(((r,s)=>{this._blockedFuncs.push([()=>{this._promise(e,t,n).then(r,s)},we])}));if(n)return Ve((()=>{var e=new Oe(((e,n)=>{this._lock();const r=t(e,n,this);r&&r.then&&r.then(e,n)}));return e.finally((()=>this._unlock())),e._lib=!0,e}));var r=new Oe(((e,n)=>{var r=t(e,n,this);r&&r.then&&r.then(e,n)}));return r._lib=!0,r}_root(){return this.parent?this.parent._root():this}waitFor(e){var t=this._root();const n=Oe.resolve(e);if(t._waitingFor)t._waitingFor=t._waitingFor.then((()=>n));else{t._waitingFor=n,t._waitingQueue=[];var r=t.idbtrans.objectStore(t.storeNames[0]);!function e(){for(++t._spinCount;t._waitingQueue.length;)t._waitingQueue.shift()();t._waitingFor&&(r.get(-1/0).onsuccess=e)}()}var s=t._waitingFor;return new Oe(((e,r)=>{n.then((n=>t._waitingQueue.push(Re(e.bind(null,n)))),(e=>t._waitingQueue.push(Re(r.bind(null,e))))).finally((()=>{t._waitingFor===s&&(t._waitingFor=null)}))}))}abort(){this.active&&(this.active=!1,this.idbtrans&&this.idbtrans.abort(),this._reject(new W.Abort))}table(e){const t=this._memoizedTables||(this._memoizedTables={});if(o(t,e))return t[e];const n=this.schema[e];if(!n)throw new W.NotFound("Table "+e+" not part of transaction");const r=new this.db.Table(e,n,this);return r.core=this.db.core.table(e),t[e]=r,r}}function Nt(e,t,n,r,s,i,o){return{name:e,keyPath:t,unique:n,multi:r,auto:s,compound:i,src:(n&&!o?"&":"")+(r?"*":"")+(s?"++":"")+Ft(t)}}function Ft(e){return"string"==typeof e?e:e?"["+[].join.call(e,"+")+"]":""}function Mt(e,t,n){return{name:e,primKey:t,indexes:n,mappedClass:null,idxByName:(r=n,s=e=>[e.name,e],r.reduce(((e,t,n)=>{var r=s(t,n);return r&&(e[r[0]]=r[1]),e}),{}))};var r,s}let Lt=e=>{try{return e.only([[]]),Lt=()=>[[]],[[]]}catch(e){return Lt=()=>rt,rt}};function Ut(e){return null==e?()=>{}:"string"==typeof e?function(e){return 1===e.split(".").length?t=>t[e]:t=>g(t,e)}(e):t=>g(t,e)}function Vt(e){return[].slice.call(e)}let zt=0;function Wt(e){return null==e?":id":"string"==typeof e?e:`[${e.join("+")}]`}function Yt(e,t,r){function s(e){if(3===e.type)return null;if(4===e.type)throw new Error("Cannot convert never type to IDBKeyRange");const{lower:n,upper:r,lowerOpen:s,upperOpen:i}=e;return void 0===n?void 0===r?null:t.upperBound(r,!!i):void 0===r?t.lowerBound(n,!!s):t.bound(n,r,!!s,!!i)}const{schema:i,hasGetAll:o}=function(e,t){const r=Vt(e.objectStoreNames);return{schema:{name:e.name,tables:r.map((e=>t.objectStore(e))).map((e=>{const{keyPath:t,autoIncrement:r}=e,s=n(t),i=null==t,o={},a={name:e.name,primaryKey:{name:null,isPrimaryKey:!0,outbound:i,compound:s,keyPath:t,autoIncrement:r,unique:!0,extractKey:Ut(t)},indexes:Vt(e.indexNames).map((t=>e.index(t))).map((e=>{const{name:t,unique:r,multiEntry:s,keyPath:i}=e,a={name:t,compound:n(i),keyPath:i,unique:r,multiEntry:s,extractKey:Ut(i)};return o[Wt(i)]=a,a})),getIndexByKeyPath:e=>o[Wt(e)]};return o[":id"]=a.primaryKey,null!=t&&(o[Wt(t)]=a.primaryKey),a}))},hasGetAll:r.length>0&&"getAll"in t.objectStore(r[0])&&!("undefined"!=typeof navigator&&/Safari/.test(navigator.userAgent)&&!/(Chrome\/|Edge\/)/.test(navigator.userAgent)&&[].concat(navigator.userAgent.match(/Safari\/(\d*)/))[1]<604)}}(e,r),a=i.tables.map((e=>function(e){const t=e.name;return{name:t,schema:e,mutate:function({trans:e,type:n,keys:r,values:i,range:o}){return new Promise(((a,u)=>{a=Re(a);const l=e.objectStore(t),c=null==l.keyPath,h="put"===n||"add"===n;if(!h&&"delete"!==n&&"deleteRange"!==n)throw new Error("Invalid operation type: "+n);const{length:d}=r||i||{length:1};if(r&&i&&r.length!==i.length)throw new Error("Given keys array must have same length as given values array.");if(0===d)return a({numFailures:0,failures:{},results:[],lastResult:void 0});let f;const p=[],y=[];let m=0;const b=e=>{++m,Bt(e)};if("deleteRange"===n){if(4===o.type)return a({numFailures:m,failures:y,results:[],lastResult:void 0});3===o.type?p.push(f=l.clear()):p.push(f=l.delete(s(o)))}else{const[e,t]=h?c?[i,r]:[i,null]:[r,null];if(h)for(let r=0;r<d;++r)p.push(f=t&&void 0!==t[r]?l[n](e[r],t[r]):l[n](e[r])),f.onerror=b;else for(let t=0;t<d;++t)p.push(f=l[n](e[t])),f.onerror=b}const g=e=>{const t=e.target.result;p.forEach(((e,t)=>null!=e.error&&(y[t]=e.error))),a({numFailures:m,failures:y,results:"delete"===n?r:p.map((e=>e.result)),lastResult:t})};f.onerror=e=>{b(e),g(e)},f.onsuccess=g}))},getMany:({trans:e,keys:n})=>new Promise(((r,s)=>{r=Re(r);const i=e.objectStore(t),o=n.length,a=new Array(o);let u,l=0,c=0;const h=e=>{const t=e.target;a[t._pos]=t.result,++c===l&&r(a)},d=It(s);for(let e=0;e<o;++e)null!=n[e]&&(u=i.get(n[e]),u._pos=e,u.onsuccess=h,u.onerror=d,++l);0===l&&r(a)})),get:({trans:e,key:n})=>new Promise(((r,s)=>{r=Re(r);const i=e.objectStore(t).get(n);i.onsuccess=e=>r(e.target.result),i.onerror=It(s)})),query:function(e){return n=>new Promise(((r,i)=>{r=Re(r);const{trans:o,values:a,limit:u,query:l}=n,c=u===1/0?void 0:u,{index:h,range:d}=l,f=o.objectStore(t),p=h.isPrimaryKey?f:f.index(h.name),y=s(d);if(0===u)return r({result:[]});if(e){const e=a?p.getAll(y,c):p.getAllKeys(y,c);e.onsuccess=e=>r({result:e.target.result}),e.onerror=It(i)}else{let e=0;const t=a||!("openKeyCursor"in p)?p.openCursor(y):p.openKeyCursor(y),n=[];t.onsuccess=s=>{const i=t.result;return i?(n.push(a?i.value:i.primaryKey),++e===u?r({result:n}):void i.continue()):r({result:n})},t.onerror=It(i)}}))}(o),openCursor:function({trans:e,values:n,query:r,reverse:i,unique:o}){return new Promise(((a,u)=>{a=Re(a);const{index:l,range:c}=r,h=e.objectStore(t),d=l.isPrimaryKey?h:h.index(l.name),f=i?o?"prevunique":"prev":o?"nextunique":"next",p=n||!("openKeyCursor"in d)?d.openCursor(s(c),f):d.openKeyCursor(s(c),f);p.onerror=It(u),p.onsuccess=Re((t=>{const n=p.result;if(!n)return void a(null);n.___id=++zt,n.done=!1;const r=n.continue.bind(n);let s=n.continuePrimaryKey;s&&(s=s.bind(n));const i=n.advance.bind(n),o=()=>{throw new Error("Cursor not stopped")};n.trans=e,n.stop=n.continue=n.continuePrimaryKey=n.advance=()=>{throw new Error("Cursor not started")},n.fail=Re(u),n.next=function(){let e=1;return this.start((()=>e--?this.continue():this.stop())).then((()=>this))},n.start=e=>{const t=new Promise(((e,t)=>{e=Re(e),p.onerror=It(t),n.fail=t,n.stop=t=>{n.stop=n.continue=n.continuePrimaryKey=n.advance=o,e(t)}})),a=()=>{if(p.result)try{e()}catch(e){n.fail(e)}else n.done=!0,n.start=()=>{throw new Error("Cursor behind last entry")},n.stop()};return p.onsuccess=Re((e=>{p.onsuccess=a,a()})),n.continue=r,n.continuePrimaryKey=s,n.advance=i,a(),t},a(n)}),u)}))},count({query:e,trans:n}){const{index:r,range:i}=e;return new Promise(((e,o)=>{const a=n.objectStore(t),u=r.isPrimaryKey?a:a.index(r.name),l=s(i),c=l?u.count(l):u.count();c.onsuccess=Re((t=>e(t.target.result))),c.onerror=It(o)}))}}}(e))),u={};return a.forEach((e=>u[e.name]=e)),{stack:"dbcore",transaction:e.transaction.bind(e),table(e){if(!u[e])throw new Error(`Table '${e}' not found`);return u[e]},MIN_KEY:-1/0,MAX_KEY:Lt(t),schema:i}}function Gt(e,t){const n=t.db,r=function(e,t,{IDBKeyRange:n,indexedDB:r},s){const i=function(e,t){return t.reduce(((e,{create:t})=>({...e,...t(e)})),e)}(Yt(t,n,s),e.dbcore);return{dbcore:i}}(e._middlewares,n,e._deps,t);e.core=r.dbcore,e.tables.forEach((t=>{const n=t.name;e.core.schema.tables.some((e=>e.name===n))&&(t.core=e.core.table(n),e[n]instanceof e.Table&&(e[n].core=t.core))}))}function Qt(e,t,n,r){n.forEach((n=>{const s=r[n];t.forEach((t=>{const r=d(t,n);(!r||"value"in r&&void 0===r.value)&&(t===e.Transaction.prototype||t instanceof e.Transaction?l(t,n,{get(){return this.table(n)},set(e){u(this,n,{value:e,writable:!0,configurable:!0,enumerable:!0})}}):t[n]=new e.Table(n,s))}))}))}function Xt(e,t){t.forEach((t=>{for(let n in t)t[n]instanceof e.Table&&delete t[n]}))}function Ht(e,t){return e._cfg.version-t._cfg.version}function Jt(e,n,r,s){const i=e._dbSchema;r.objectStoreNames.contains("$meta")&&!i.$meta&&(i.$meta=Mt("$meta",on("")[0],[]),e._storeNames.push("$meta"));const o=e._createTransaction("readwrite",e._storeNames,i);o.create(r),o._completion.catch(s);const a=o._reject.bind(o),u=we.transless||we;Ve((()=>{if(we.trans=o,we.transless=u,0!==n)return Gt(e,r),function(e,t,n){return t.storeNames.includes("$meta")?t.table("$meta").get("version").then((e=>null!=e?e:n)):Oe.resolve(n)}(0,o,n).then((n=>function(e,n,r,s){const i=[],o=e._versions;let a=e._dbSchema=rn(e,e.idbdb,s);const u=o.filter((e=>e._cfg.version>=n));if(0===u.length)return Oe.resolve();function l(){return i.length?Oe.resolve(i.shift()(r.idbtrans)).then(l):Oe.resolve()}return u.forEach((o=>{i.push((()=>{const i=a,u=o._cfg.dbschema;sn(e,i,s),sn(e,u,s),a=e._dbSchema=u;const l=Zt(i,u);l.add.forEach((e=>{en(s,e[0],e[1].primKey,e[1].indexes)})),l.change.forEach((e=>{if(e.recreate)throw new W.Upgrade("Not yet support for changing primary key");{const t=s.objectStore(e.name);e.add.forEach((e=>nn(t,e))),e.change.forEach((e=>{t.deleteIndex(e.name),nn(t,e)})),e.del.forEach((e=>t.deleteIndex(e)))}}));const c=o._cfg.contentUpgrade;if(c&&o._cfg.version>n){Gt(e,s),r._memoizedTables={};let n=w(u);l.del.forEach((e=>{n[e]=i[e]})),Xt(e,[e.Transaction.prototype]),Qt(e,[e.Transaction.prototype],t(n),n),r.schema=n;const o=B(c);let a;o&&ze();const h=Oe.follow((()=>{if(a=c(r),a&&o){var e=We.bind(null,null);a.then(e,e)}}));return a&&"function"==typeof a.then?Oe.resolve(a):h.then((()=>a))}})),i.push((t=>{!function(e,t){[].slice.call(t.db.objectStoreNames).forEach((n=>null==e[n]&&t.db.deleteObjectStore(n)))}(o._cfg.dbschema,t),Xt(e,[e.Transaction.prototype]),Qt(e,[e.Transaction.prototype],e._storeNames,e._dbSchema),r.schema=e._dbSchema})),i.push((t=>{e.idbdb.objectStoreNames.contains("$meta")&&(Math.ceil(e.idbdb.version/10)===o._cfg.version?(e.idbdb.deleteObjectStore("$meta"),delete e._dbSchema.$meta,e._storeNames=e._storeNames.filter((e=>"$meta"!==e))):t.objectStore("$meta").put(o._cfg.version,"version"))}))})),l().then((()=>{tn(a,s)}))}(e,n,o,r))).catch(a);t(i).forEach((e=>{en(r,e,i[e].primKey,i[e].indexes)})),Gt(e,r),Oe.follow((()=>e.on.populate.fire(o))).catch(a)}))}function Zt(e,t){const n={del:[],add:[],change:[]};let r;for(r in e)t[r]||n.del.push(r);for(r in t){const s=e[r],i=t[r];if(s){const e={name:r,def:i,recreate:!1,del:[],add:[],change:[]};if(""+(s.primKey.keyPath||"")!=""+(i.primKey.keyPath||"")||s.primKey.auto!==i.primKey.auto)e.recreate=!0,n.change.push(e);else{const t=s.idxByName,r=i.idxByName;let o;for(o in t)r[o]||e.del.push(o);for(o in r){const n=t[o],s=r[o];n?n.src!==s.src&&e.change.push(s):e.add.push(s)}(e.del.length>0||e.add.length>0||e.change.length>0)&&n.change.push(e)}}else n.add.push([r,i])}return n}function en(e,t,n,r){const s=e.db.createObjectStore(t,n.keyPath?{keyPath:n.keyPath,autoIncrement:n.auto}:{autoIncrement:n.auto});return r.forEach((e=>nn(s,e))),s}function tn(e,n){t(e).forEach((t=>{n.db.objectStoreNames.contains(t)||(se&&console.debug("Dexie: Creating missing table",t),en(n,t,e[t].primKey,e[t].indexes))}))}function nn(e,t){e.createIndex(t.name,t.keyPath,{unique:t.unique,multiEntry:t.multi})}function rn(e,t,n){const r={};return p(t.objectStoreNames,0).forEach((e=>{const t=n.objectStore(e);let s=t.keyPath;const i=Nt(Ft(s),s||"",!0,!1,!!t.autoIncrement,s&&"string"!=typeof s,!0),o=[];for(let e=0;e<t.indexNames.length;++e){const n=t.index(t.indexNames[e]);s=n.keyPath;var a=Nt(n.name,s,!!n.unique,!!n.multiEntry,!1,s&&"string"!=typeof s,!1);o.push(a)}r[e]=Mt(e,i,o)})),r}function sn(t,n,r){const s=r.db.objectStoreNames;for(let e=0;e<s.length;++e){const i=s[e],o=r.objectStore(i);t._hasGetAll="getAll"in o;for(let e=0;e<o.indexNames.length;++e){const t=o.indexNames[e],r=o.index(t).keyPath,s="string"==typeof r?r:"["+p(r).join("+")+"]";if(n[i]){const e=n[i].idxByName[s];e&&(e.name=t,delete n[i].idxByName[s],n[i].idxByName[t]=e)}}}"undefined"!=typeof navigator&&/Safari/.test(navigator.userAgent)&&!/(Chrome\/|Edge\/)/.test(navigator.userAgent)&&e.WorkerGlobalScope&&e instanceof e.WorkerGlobalScope&&[].concat(navigator.userAgent.match(/Safari\/(\d*)/))[1]<604&&(t._hasGetAll=!1)}function on(e){return e.split(",").map(((e,t)=>{const r=(e=e.trim()).replace(/([&*]|\+\+)/g,""),s=/^\[/.test(r)?r.match(/^\[(.*)\]$/)[1].split("+"):r;return Nt(r,s||null,/\&/.test(e),/\*/.test(e),/\+\+/.test(e),n(s),0===t)}))}class an{_parseStoresSpec(e,n){t(e).forEach((t=>{if(null!==e[t]){var r=on(e[t]),s=r.shift();if(s.unique=!0,s.multi)throw new W.Schema("Primary key cannot be multi-valued");r.forEach((e=>{if(e.auto)throw new W.Schema("Only primary key can be marked as autoIncrement (++)");if(!e.keyPath)throw new W.Schema("Index must have a name and cannot be an empty string")})),n[t]=Mt(t,s,r)}}))}stores(e){const n=this.db;this._cfg.storesSource=this._cfg.storesSource?r(this._cfg.storesSource,e):e;const s=n._versions,i={};let o={};return s.forEach((e=>{r(i,e._cfg.storesSource),o=e._cfg.dbschema={},e._parseStoresSpec(i,o)})),n._dbSchema=o,Xt(n,[n._allTables,n,n.Transaction.prototype]),Qt(n,[n._allTables,n,n.Transaction.prototype,this._cfg.tables],t(o),o),n._storeNames=t(o),this}upgrade(e){return this._cfg.contentUpgrade=re(this._cfg.contentUpgrade||Q,e),this}}function un(e,t){let n=e._dbNamesDB;return n||(n=e._dbNamesDB=new Xn("__dbnames",{addons:[],indexedDB:e,IDBKeyRange:t}),n.version(1).stores({dbnames:"name"})),n.table("dbnames")}function ln(e){return e&&"function"==typeof e.databases}function cn(e){return Ve((function(){return we.letThrough=!0,e()}))}function hn(){var e;return!navigator.userAgentData&&/Safari\//.test(navigator.userAgent)&&!/Chrom(e|ium)\//.test(navigator.userAgent)&&indexedDB.databases?new Promise((function(t){var n=function(){return indexedDB.databases().finally(t)};e=setInterval(n,100),n()})).finally((function(){return clearInterval(e)})):Promise.resolve()}function dn(e){return!("from"in e)}const fn=function(e,t){if(!this){const t=new fn;return e&&"d"in e&&r(t,e),t}r(this,arguments.length?{d:1,from:e,to:arguments.length>1?t:e}:{d:0})};function pn(e,t,n){const s=ct(t,n);if(isNaN(s))return;if(s>0)throw RangeError();if(dn(e))return r(e,{from:t,to:n,d:1});const i=e.l,o=e.r;if(ct(n,e.from)<0)return i?pn(i,t,n):e.l={from:t,to:n,d:1,l:null,r:null},gn(e);if(ct(t,e.to)>0)return o?pn(o,t,n):e.r={from:t,to:n,d:1,l:null,r:null},gn(e);ct(t,e.from)<0&&(e.from=t,e.l=null,e.d=o?o.d+1:1),ct(n,e.to)>0&&(e.to=n,e.r=null,e.d=e.l?e.l.d+1:1);const a=!e.r;i&&!e.l&&yn(e,i),o&&a&&yn(e,o)}function yn(e,t){dn(t)||function e(t,{from:n,to:r,l:s,r:i}){pn(t,n,r),s&&e(t,s),i&&e(t,i)}(e,t)}function mn(e,t){const n=bn(t);let r=n.next();if(r.done)return!1;let s=r.value;const i=bn(e);let o=i.next(s.from),a=o.value;for(;!r.done&&!o.done;){if(ct(a.from,s.to)<=0&&ct(a.to,s.from)>=0)return!0;ct(s.from,a.from)<0?s=(r=n.next(a.from)).value:a=(o=i.next(s.from)).value}return!1}function bn(e){let t=dn(e)?null:{s:0,n:e};return{next(e){const n=arguments.length>0;for(;t;)switch(t.s){case 0:if(t.s=1,n)for(;t.n.l&&ct(e,t.n.from)<0;)t={up:t,n:t.n.l,s:1};else for(;t.n.l;)t={up:t,n:t.n.l,s:1};case 1:if(t.s=2,!n||ct(e,t.n.to)<=0)return{value:t.n,done:!1};case 2:if(t.n.r){t.s=3,t={up:t,n:t.n.r,s:0};continue}case 3:t=t.up}return{done:!0}}}}function gn(e){const t=(e.r?.d||0)-(e.l?.d||0),n=t>1?"r":t<-1?"l":"";if(n){const t="r"===n?"l":"r",r={...e},s=e[n];e.from=s.from,e.to=s.to,e[n]=s[n],r[n]=s[t],e[t]=r,r.d=vn(r)}e.d=vn(e)}function vn({r:e,l:t}){return(e?t?Math.max(e.d,t.d):e.d:t?t.d:0)+1}function wn(e,n){return t(n).forEach((t=>{e[t]?yn(e[t],n[t]):e[t]=P(n[t])})),e}function _n(e,t){return e.all||t.all||Object.keys(e).some((n=>t[n]&&mn(t[n],e[n])))}a(fn.prototype,{add(e){return yn(this,e),this},addKey(e){return pn(this,e,e),this},addKeys(e){return e.forEach((e=>pn(this,e,e))),this},hasKey(e){const t=bn(this).next(e).value;return t&&ct(t.from,e)<=0&&ct(t.to,e)>=0},[j](){return bn(this)}});const xn={};let kn={},On=!1;function Pn(e,t=!1){wn(kn,e),On||(On=!0,setTimeout((()=>{On=!1;const e=kn;kn={},Kn(e,!1)}),0))}function Kn(e,t=!1){const n=new Set;if(e.all)for(const r of Object.values(xn))En(r,e,n,t);else for(const r in e){const s=/^idb\:\/\/(.*)\/(.*)\//.exec(r);if(s){const[,r,i]=s,o=xn[`idb://${r}/${i}`];o&&En(o,e,n,t)}}n.forEach((e=>e()))}function En(e,t,n,r){const s=[];for(const[i,o]of Object.entries(e.queries.query)){const e=[];for(const s of o)_n(t,s.obsSet)?s.subscribers.forEach((e=>n.add(e))):r&&e.push(s);r&&s.push([i,e])}if(r)for(const[t,n]of s)e.queries.query[t]=n}function Sn(e){const n=e._state,{indexedDB:r}=e._deps;if(n.isBeingOpened||e.idbdb)return n.dbReadyPromise.then((()=>n.dbOpenError?tt(n.dbOpenError):e));n.isBeingOpened=!0,n.dbOpenError=null,n.openComplete=!1;const s=n.openCanceller;let i=Math.round(10*e.verno),o=!1;function a(){if(n.openCanceller!==s)throw new W.DatabaseClosed("db.open() was cancelled")}let u=n.dbReadyResolve,l=null,c=!1;const h=()=>new Oe(((s,u)=>{if(a(),!r)throw new W.MissingAPI;const d=e.name,f=n.autoSchema||!i?r.open(d):r.open(d,i);if(!f)throw new W.MissingAPI;f.onerror=It(u),f.onblocked=Re(e._fireOnBlocked),f.onupgradeneeded=Re((t=>{if(l=f.transaction,n.autoSchema&&!e._options.allowEmptyDB){f.onerror=Bt,l.abort(),f.result.close();const e=r.deleteDatabase(d);e.onsuccess=e.onerror=Re((()=>{u(new W.NoSuchDatabase(`Database ${d} doesnt exist`))}))}else{l.onerror=It(u);const n=t.oldVersion>Math.pow(2,62)?0:t.oldVersion;c=n<1,e.idbdb=f.result,o&&function(e,t){tn(e._dbSchema,t),t.db.version%10!=0||t.objectStoreNames.contains("$meta")||t.db.createObjectStore("$meta").add(Math.ceil(t.db.version/10-1),"version");const n=rn(0,e.idbdb,t);sn(e,e._dbSchema,t);const r=Zt(n,e._dbSchema);for(const e of r.change){if(e.change.length||e.recreate)return void console.warn(`Unable to patch indexes of table ${e.name} because it has changes on the type of index or primary key.`);const n=t.objectStore(e.name);e.add.forEach((t=>{se&&console.debug(`Dexie upgrade patch: Creating missing index ${e.name}.${t.src}`),nn(n,t)}))}}(e,l),Jt(e,n/10,l,u)}}),u),f.onsuccess=Re((()=>{l=null;const r=e.idbdb=f.result,a=p(r.objectStoreNames);if(a.length>0)try{const l=r.transaction(1===(u=a).length?u[0]:u,"readonly");if(n.autoSchema)!function(e,n,r){e.verno=n.version/10;const s=e._dbSchema=rn(0,n,r);e._storeNames=p(n.objectStoreNames,0),Qt(e,[e._allTables],t(s),s)}(e,r,l);else if(sn(e,e._dbSchema,l),!function(e,t){const n=Zt(rn(0,e.idbdb,t),e._dbSchema);return!(n.add.length||n.change.some((e=>e.add.length||e.change.length)))}(e,l)&&!o)return console.warn("Dexie SchemaDiff: Schema was extended without increasing the number passed to db.version(). Dexie will add missing parts and increment native version number to workaround this."),r.close(),i=r.version+1,o=!0,s(h());Gt(e,l)}catch(e){}var u;it.push(e),r.onversionchange=Re((t=>{n.vcFired=!0,e.on("versionchange").fire(t)})),r.onclose=Re((t=>{e.on("close").fire(t)})),c&&function({indexedDB:e,IDBKeyRange:t},n){!ln(e)&&"__dbnames"!==n&&un(e,t).put({name:n}).catch(Q)}(e._deps,d),s()}),u)})).catch((e=>{switch(e?.name){case"UnknownError":if(n.PR1398_maxLoop>0)return n.PR1398_maxLoop--,console.warn("Dexie: Workaround for Chrome UnknownError on open()"),h();break;case"VersionError":if(i>0)return i=0,h()}return Oe.reject(e)}));return Oe.race([s,("undefined"==typeof navigator?Oe.resolve():hn()).then(h)]).then((()=>(a(),n.onReadyBeingFired=[],Oe.resolve(cn((()=>e.on.ready.fire(e.vip)))).then((function t(){if(n.onReadyBeingFired.length>0){let r=n.onReadyBeingFired.reduce(re,Q);return n.onReadyBeingFired=[],Oe.resolve(cn((()=>r(e.vip)))).then(t)}}))))).finally((()=>{n.openCanceller===s&&(n.onReadyBeingFired=null,n.isBeingOpened=!1)})).catch((t=>{n.dbOpenError=t;try{l&&l.abort()}catch{}return s===n.openCanceller&&e._close(),tt(t)})).finally((()=>{n.openComplete=!0,u()})).then((()=>{if(c){const t={};e.tables.forEach((n=>{n.schema.indexes.forEach((r=>{r.name&&(t[`idb://${e.name}/${n.name}/${r.name}`]=new fn(-1/0,[[[]]]))})),t[`idb://${e.name}/${n.name}/`]=t[`idb://${e.name}/${n.name}/:dels`]=new fn(-1/0,[[[]]])})),Rt("storagemutated").fire(t),Kn(t,!0)}return e}))}function An(e){var t=t=>e.next(t),r=i(t),s=i((t=>e.throw(t)));function i(e){return t=>{var i=e(t),o=i.value;return i.done?o:o&&"function"==typeof o.then?o.then(r,s):n(o)?Promise.all(o).then(r,s):r(o)}}return i(t)()}function Cn(e,t,n){var r=arguments.length;if(r<2)throw new W.InvalidArgument("Too few arguments");for(var s=new Array(r-1);--r;)s[r-1]=arguments[r];n=s.pop();var i=x(s);return[e,i,n]}function jn(e,t,n,r,s){return Oe.resolve().then((()=>{const i=we.transless||we,o=e._createTransaction(t,n,e._dbSchema,r);o.explicit=!0;const a={trans:o,transless:i};if(r)o.idbtrans=r.idbtrans;else try{o.create(),o.idbtrans._explicit=!0,e._state.PR1398_maxLoop=3}catch(r){return r.name===V.InvalidState&&e.isOpen()&&--e._state.PR1398_maxLoop>0?(console.warn("Dexie: Need to reopen db"),e.close({disableAutoOpen:!1}),e.open().then((()=>jn(e,t,n,null,s)))):tt(r)}const u=B(s);let l;u&&ze();const c=Oe.follow((()=>{if(l=s.call(o,o),l)if(u){var e=We.bind(null,null);l.then(e,e)}else"function"==typeof l.next&&"function"==typeof l.throw&&(l=An(l))}),a);return(l&&"function"==typeof l.then?Oe.resolve(l).then((e=>o.active?e:tt(new W.PrematureCommit("Transaction committed too early. See http://bit.ly/2kdckMn")))):c.then((()=>l))).then((e=>(r&&o._resolve(),o._completion.then((()=>e))))).catch((e=>(o._reject(e),tt(e))))}))}function qn(e,t,r){const s=n(e)?e.slice():[e];for(let e=0;e<r;++e)s.push(t);return s}const Dn={stack:"dbcore",name:"VirtualIndexMiddleware",level:1,create:function(e){return{...e,table(t){const n=e.table(t),{schema:r}=n,s={},i=[];function o(e,t,n){const r=Wt(e),a=s[r]=s[r]||[],u=null==e?0:"string"==typeof e?1:e.length,l=t>0,c={...n,name:l?`${r}(virtual-from:${n.name})`:n.name,lowLevelIndex:n,isVirtual:l,keyTail:t,keyLength:u,extractKey:Ut(e),unique:!l&&n.unique};if(a.push(c),c.isPrimaryKey||i.push(c),u>1){o(2===u?e[0]:e.slice(0,u-1),t+1,n)}return a.sort(((e,t)=>e.keyTail-t.keyTail)),c}const a=o(r.primaryKey.keyPath,0,r.primaryKey);s[":id"]=[a];for(const e of r.indexes)o(e.keyPath,0,e);function u(t){const n=t.query.index;return n.isVirtual?{...t,query:{index:n.lowLevelIndex,range:(r=t.query.range,s=n.keyTail,{type:1===r.type?2:r.type,lower:qn(r.lower,r.lowerOpen?e.MAX_KEY:e.MIN_KEY,s),lowerOpen:!0,upper:qn(r.upper,r.upperOpen?e.MIN_KEY:e.MAX_KEY,s),upperOpen:!0})}}:t;var r,s}const l={...n,schema:{...r,primaryKey:a,indexes:i,getIndexByKeyPath:function(e){const t=s[Wt(e)];return t&&t[0]}},count:e=>n.count(u(e)),query:e=>n.query(u(e)),openCursor(t){const{keyTail:r,isVirtual:s,keyLength:i}=t.query.index;if(!s)return n.openCursor(t);return n.openCursor(u(t)).then((n=>n&&function(n){const s=Object.create(n,{continue:{value:function(s){null!=s?n.continue(qn(s,t.reverse?e.MAX_KEY:e.MIN_KEY,r)):t.unique?n.continue(n.key.slice(0,i).concat(t.reverse?e.MIN_KEY:e.MAX_KEY,r)):n.continue()}},continuePrimaryKey:{value(t,s){n.continuePrimaryKey(qn(t,e.MAX_KEY,r),s)}},primaryKey:{get:()=>n.primaryKey},key:{get(){const e=n.key;return 1===i?e[0]:e.slice(0,i)}},value:{get:()=>n.value}});return s}(n)))}};return l}}}};function Tn(e,n,r,s){return r=r||{},s=s||"",t(e).forEach((t=>{if(o(n,t)){var i=e[t],a=n[t];if("object"==typeof i&&"object"==typeof a&&i&&a){const e=C(i);e!==C(a)?r[s+t]=n[t]:"Object"===e?Tn(i,a,r,s+t+"."):i!==a&&(r[s+t]=n[t])}else i!==a&&(r[s+t]=n[t])}else r[s+t]=void 0})),t(n).forEach((t=>{o(e,t)||(r[s+t]=n[t])})),r}function In(e,t){return"delete"===t.type?t.keys:t.keys||t.values.map(e.extractKey)}const Bn={stack:"dbcore",name:"HooksMiddleware",level:2,create:e=>({...e,table(t){const n=e.table(t),{primaryKey:r}=n.schema,s={...n,mutate(e){const s=we.trans,{deleting:i,creating:a,updating:u}=s.table(t).hook;switch(e.type){case"add":if(a.fire===Q)break;return s._promise("readwrite",(()=>l(e)),!0);case"put":if(a.fire===Q&&u.fire===Q)break;return s._promise("readwrite",(()=>l(e)),!0);case"delete":if(i.fire===Q)break;return s._promise("readwrite",(()=>l(e)),!0);case"deleteRange":if(i.fire===Q)break;return s._promise("readwrite",(()=>function(e){return c(e.trans,e.range,1e4)}(e)),!0)}return n.mutate(e);function l(e){const t=we.trans,s=e.keys||In(r,e);if(!s)throw new Error("Keys missing");return"delete"!==(e="add"===e.type||"put"===e.type?{...e,keys:s}:{...e}).type&&(e.values=[...e.values]),e.keys&&(e.keys=[...e.keys]),function(e,t,n){return"add"===t.type?Promise.resolve([]):e.getMany({trans:t.trans,keys:n,cache:"immutable"})}(n,e,s).then((l=>{const c=s.map(((n,s)=>{const c=l[s],h={onerror:null,onsuccess:null};if("delete"===e.type)i.fire.call(h,n,c,t);else if("add"===e.type||void 0===c){const i=a.fire.call(h,n,e.values[s],t);null==n&&null!=i&&(n=i,e.keys[s]=n,r.outbound||v(e.values[s],r.keyPath,n))}else{const r=Tn(c,e.values[s]),i=u.fire.call(h,r,n,c,t);if(i){const t=e.values[s];Object.keys(i).forEach((e=>{o(t,e)?t[e]=i[e]:v(t,e,i[e])}))}}return h}));return n.mutate(e).then((({failures:t,results:n,numFailures:r,lastResult:i})=>{for(let r=0;r<s.length;++r){const i=n?n[r]:s[r],o=c[r];null==i?o.onerror&&o.onerror(t[r]):o.onsuccess&&o.onsuccess("put"===e.type&&l[r]?e.values[r]:i)}return{failures:t,results:n,numFailures:r,lastResult:i}})).catch((e=>(c.forEach((t=>t.onerror&&t.onerror(e))),Promise.reject(e))))}))}function c(e,t,s){return n.query({trans:e,values:!1,query:{index:r,range:t},limit:s}).then((({result:n})=>l({type:"delete",keys:n,trans:e}).then((r=>r.numFailures>0?Promise.reject(r.failures[0]):n.length<s?{failures:[],numFailures:0,lastResult:void 0}:c(e,{...t,lower:n[n.length-1],lowerOpen:!0},s)))))}}};return s}})};function Rn(e,t,n){try{if(!t)return null;if(t.keys.length<e.length)return null;const r=[];for(let s=0,i=0;s<t.keys.length&&i<e.length;++s)0===ct(t.keys[s],e[i])&&(r.push(n?E(t.values[s]):t.values[s]),++i);return r.length===e.length?r:null}catch{return null}}const $n={stack:"dbcore",level:-1,create:e=>({table:t=>{const n=e.table(t);return{...n,getMany:e=>{if(!e.cache)return n.getMany(e);const t=Rn(e.keys,e.trans._cache,"clone"===e.cache);return t?Oe.resolve(t):n.getMany(e).then((t=>(e.trans._cache={keys:e.keys,values:"clone"===e.cache?E(t):t},t)))},mutate:e=>("add"!==e.type&&(e.trans._cache=null),n.mutate(e))}}})};function Nn(e,t){return"readonly"===e.trans.mode&&!!e.subscr&&!e.trans.explicit&&"disabled"!==e.trans.db._options.cache&&!t.schema.primaryKey.outbound}function Fn(e,t){switch(e){case"query":return t.values&&!t.unique;case"get":case"getMany":case"count":case"openCursor":return!1}}const Mn={stack:"dbcore",level:0,name:"Observability",create:e=>{const r=e.schema.name,s=new fn(e.MIN_KEY,e.MAX_KEY);return{...e,transaction:(t,n,r)=>{if(we.subscr&&"readonly"!==n)throw new W.ReadOnly(`Readwrite transaction in liveQuery context. Querier source: ${we.querier}`);return e.transaction(t,n,r)},table:i=>{const o=e.table(i),{schema:a}=o,{primaryKey:u,indexes:l}=a,{extractKey:c,outbound:h}=u,d=u.autoIncrement&&l.filter((e=>e.compound&&e.keyPath.includes(u.keyPath))),f={...o,mutate:t=>{const l=t.trans,c=t.mutatedParts||(t.mutatedParts={}),h=e=>{const t=`idb://${r}/${i}/${e}`;return c[t]||(c[t]=new fn)},f=h(""),p=h(":dels"),{type:y}=t;let[m,b]="deleteRange"===t.type?[t.range]:"delete"===t.type?[t.keys]:t.values.length<50?[In(u,t).filter((e=>e)),t.values]:[];const g=t.trans._cache;if(n(m)){f.addKeys(m);const e="delete"===y||m.length===b.length?Rn(m,g):null;e||p.addKeys(m),(e||b)&&function(e,t,r,s){function i(t){const i=e(t.name||"");function o(e){return null!=e?t.extractKey(e):null}const a=e=>t.multiEntry&&n(e)?e.forEach((e=>i.addKey(e))):i.addKey(e);(r||s).forEach(((e,t)=>{const n=r&&o(r[t]),i=s&&o(s[t]);0!==ct(n,i)&&(null!=n&&a(n),null!=i&&a(i))}))}t.indexes.forEach(i)}(h,a,e,b)}else if(m){const t={from:m.lower??e.MIN_KEY,to:m.upper??e.MAX_KEY};p.add(t),f.add(t)}else f.add(s),p.add(s),a.indexes.forEach((e=>h(e.name).add(s)));return o.mutate(t).then((e=>(!m||"add"!==t.type&&"put"!==t.type||(f.addKeys(e.results),d&&d.forEach((n=>{const r=t.values.map((e=>n.extractKey(e))),s=n.keyPath.findIndex((e=>e===u.keyPath));for(let t=0,n=e.results.length;t<n;++t)r[t][s]=e.results[t];h(n.name).addKeys(r)}))),l.mutatedParts=wn(l.mutatedParts||{},c),e)))}},p=({query:{index:t,range:n}})=>[t,new fn(n.lower??e.MIN_KEY,n.upper??e.MAX_KEY)],y={get:e=>[u,new fn(e.key)],getMany:e=>[u,(new fn).addKeys(e.keys)],count:p,query:p,openCursor:p};return t(y).forEach((e=>{f[e]=function(t){const{subscr:n}=we,a=!!n;let u=Nn(we,o)&&Fn(e,t);const l=u?t.obsSet={}:n;if(a){const n=e=>{const t=`idb://${r}/${i}/${e}`;return l[t]||(l[t]=new fn)},a=n(""),u=n(":dels"),[d,f]=y[e](t);if("query"===e&&d.isPrimaryKey&&!t.values?u.add(f):n(d.name||"").add(f),!d.isPrimaryKey){if("count"!==e){const n="query"===e&&h&&t.values&&o.query({...t,values:!1});return o[e].apply(this,arguments).then((r=>{if("query"===e){if(h&&t.values)return n.then((({result:e})=>(a.addKeys(e),r)));const e=t.values?r.result.map(c):r.result;t.values?a.addKeys(e):u.addKeys(e)}else if("openCursor"===e){const e=r,n=t.values;return e&&Object.create(e,{key:{get:()=>(u.addKey(e.primaryKey),e.key)},primaryKey:{get(){const t=e.primaryKey;return u.addKey(t),t}},value:{get:()=>(n&&a.addKey(e.primaryKey),e.value)}})}return r}))}u.add(s)}}return o[e].apply(this,arguments)}})),f}}}};function Ln(e,t,r){if(0===r.numFailures)return t;if("deleteRange"===t.type)return null;const s=t.keys?t.keys.length:"values"in t&&t.values?t.values.length:1;if(r.numFailures===s)return null;const i={...t};return n(i.keys)&&(i.keys=i.keys.filter(((e,t)=>!(t in r.failures)))),"values"in i&&n(i.values)&&(i.values=i.values.filter(((e,t)=>!(t in r.failures)))),i}function Un(e,t){return function(e,t){return void 0===t.lower||(t.lowerOpen?ct(e,t.lower)>0:ct(e,t.lower)>=0)}(e,t)&&function(e,t){return void 0===t.upper||(t.upperOpen?ct(e,t.upper)<0:ct(e,t.upper)<=0)}(e,t)}function Vn(e,t,r,s,i,o){if(!r||0===r.length)return e;const a=t.query.index,{multiEntry:u}=a,l=t.query.range,c=s.schema.primaryKey.extractKey,h=a.extractKey,d=(a.lowLevelIndex||a).extractKey;let f=r.reduce(((e,r)=>{let s=e;const i=[];if("add"===r.type||"put"===r.type){const e=new fn;for(let t=r.values.length-1;t>=0;--t){const s=r.values[t],o=c(s);if(e.hasKey(o))continue;const a=h(s);(u&&n(a)?a.some((e=>Un(e,l))):Un(a,l))&&(e.addKey(o),i.push(s))}}switch(r.type){case"add":{const n=(new fn).addKeys(t.values?e.map((e=>c(e))):e);s=e.concat(t.values?i.filter((e=>{const t=c(e);return!n.hasKey(t)&&(n.addKey(t),!0)})):i.map((e=>c(e))).filter((e=>!n.hasKey(e)&&(n.addKey(e),!0))));break}case"put":{const n=(new fn).addKeys(r.values.map((e=>c(e))));s=e.filter((e=>!n.hasKey(t.values?c(e):e))).concat(t.values?i:i.map((e=>c(e))));break}case"delete":const n=(new fn).addKeys(r.keys);s=e.filter((e=>!n.hasKey(t.values?c(e):e)));break;case"deleteRange":const o=r.range;s=e.filter((e=>!Un(c(e),o)))}return s}),e);return f===e?e:(f.sort(((e,t)=>ct(d(e),d(t))||ct(c(e),c(t)))),t.limit&&t.limit<1/0&&(f.length>t.limit?f.length=t.limit:e.length===t.limit&&f.length<t.limit&&(i.dirty=!0)),o?Object.freeze(f):f)}function zn(e,t){return 0===ct(e.lower,t.lower)&&0===ct(e.upper,t.upper)&&!!e.lowerOpen==!!t.lowerOpen&&!!e.upperOpen==!!t.upperOpen}function Wn(e,t){return function(e,t,n,r){if(void 0===e)return void 0!==t?-1:0;if(void 0===t)return 1;const s=ct(e,t);if(0===s){if(n&&r)return 0;if(n)return 1;if(r)return-1}return s}(e.lower,t.lower,e.lowerOpen,t.lowerOpen)<=0&&function(e,t,n,r){if(void 0===e)return void 0!==t?1:0;if(void 0===t)return-1;const s=ct(e,t);if(0===s){if(n&&r)return 0;if(n)return-1;if(r)return 1}return s}(e.upper,t.upper,e.upperOpen,t.upperOpen)>=0}function Yn(e,t,n,r){e.subscribers.add(n),r.addEventListener("abort",(()=>{e.subscribers.delete(n),0===e.subscribers.size&&function(e,t){setTimeout((()=>{0===e.subscribers.size&&D(t,e)}),3e3)}(e,t)}))}const Gn={stack:"dbcore",level:0,name:"Cache",create:e=>{const t=e.schema.name,n={...e,transaction:(n,r,s)=>{const i=e.transaction(n,r,s);if("readwrite"===r){const s=new AbortController,{signal:o}=s,a=o=>()=>{if(s.abort(),"readwrite"===r){const r=new Set;for(const s of n){const n=xn[`idb://${t}/${s}`];if(n){const t=e.table(s),a=n.optimisticOps.filter((e=>e.trans===i));if(i._explicit&&o&&i.mutatedParts)for(const e of Object.values(n.queries.query))for(const t of e.slice())_n(t.obsSet,i.mutatedParts)&&(D(e,t),t.subscribers.forEach((e=>r.add(e))));else if(a.length>0){n.optimisticOps=n.optimisticOps.filter((e=>e.trans!==i));for(const e of Object.values(n.queries.query))for(const n of e.slice())if(null!=n.res&&i.mutatedParts)if(o&&!n.dirty){const s=Object.isFrozen(n.res),i=Vn(n.res,n.req,a,t,n,s);n.dirty?(D(e,n),n.subscribers.forEach((e=>r.add(e)))):i!==n.res&&(n.res=i,n.promise=Oe.resolve({result:i}))}else n.dirty&&D(e,n),n.subscribers.forEach((e=>r.add(e)))}}}r.forEach((e=>e()))}};i.addEventListener("abort",a(!1),{signal:o}),i.addEventListener("error",a(!1),{signal:o}),i.addEventListener("complete",a(!0),{signal:o})}return i},table(n){const r=e.table(n),s=r.schema.primaryKey,i={...r,mutate(e){const i=we.trans;if(s.outbound||"disabled"===i.db._options.cache||i.explicit||"readwrite"!==i.idbtrans.mode)return r.mutate(e);const o=xn[`idb://${t}/${n}`];if(!o)return r.mutate(e);const a=r.mutate(e);return"add"!==e.type&&"put"!==e.type||!(e.values.length>=50||In(s,e).some((e=>null==e)))?(o.optimisticOps.push(e),e.mutatedParts&&Pn(e.mutatedParts),a.then((t=>{if(t.numFailures>0){D(o.optimisticOps,e);const n=Ln(0,e,t);n&&o.optimisticOps.push(n),e.mutatedParts&&Pn(e.mutatedParts)}})),a.catch((()=>{D(o.optimisticOps,e),e.mutatedParts&&Pn(e.mutatedParts)}))):a.then((t=>{const n=Ln(0,{...e,values:e.values.map(((e,n)=>{if(t.failures[n])return e;const r=s.keyPath?.includes(".")?E(e):{...e};return v(r,s.keyPath,t.results[n]),r}))},t);o.optimisticOps.push(n),queueMicrotask((()=>e.mutatedParts&&Pn(e.mutatedParts)))})),a},query(e){if(!Nn(we,r)||!Fn("query",e))return r.query(e);const s="immutable"===we.trans?.db._options.cache,{requery:i,signal:o}=we;let[a,u,l,c]=function(e,t,n,r){const s=xn[`idb://${e}/${t}`];if(!s)return[];const i=s.queries[n];if(!i)return[null,!1,s,null];const o=i[(r.query?r.query.index.name:null)||""];if(!o)return[null,!1,s,null];switch(n){case"query":const e=o.find((e=>e.req.limit===r.limit&&e.req.values===r.values&&zn(e.req.query.range,r.query.range)));return e?[e,!0,s,o]:[o.find((e=>("limit"in e.req?e.req.limit:1/0)>=r.limit&&(!r.values||e.req.values)&&Wn(e.req.query.range,r.query.range))),!1,s,o];case"count":const t=o.find((e=>zn(e.req.query.range,r.query.range)));return[t,!!t,s,o]}}(t,n,"query",e);if(a&&u)a.obsSet=e.obsSet;else{const i=r.query(e).then((e=>{const t=e.result;if(a&&(a.res=t),s){for(let e=0,n=t.length;e<n;++e)Object.freeze(t[e]);Object.freeze(t)}else e.result=E(t);return e})).catch((e=>(c&&a&&D(c,a),Promise.reject(e))));a={obsSet:e.obsSet,promise:i,subscribers:new Set,type:"query",req:e,dirty:!1},c?c.push(a):(c=[a],l||(l=xn[`idb://${t}/${n}`]={queries:{query:{},count:{}},objs:new Map,optimisticOps:[],unsignaledParts:{}}),l.queries.query[e.query.index.name||""]=c)}return Yn(a,c,i,o),a.promise.then((t=>({result:Vn(t.result,e,l?.optimisticOps,r,a,s)})))}};return i}};return n}};function Qn(e,t){return new Proxy(e,{get:(e,n,r)=>"db"===n?t:Reflect.get(e,n,r)})}class Xn{constructor(e,t){this._middlewares={},this.verno=0;const n=Xn.dependencies;this._options=t={addons:Xn.addons,autoOpen:!0,indexedDB:n.indexedDB,IDBKeyRange:n.IDBKeyRange,cache:"cloned",...t},this._deps={indexedDB:t.indexedDB,IDBKeyRange:t.IDBKeyRange};const{addons:r}=t;this._dbSchema={},this._versions=[],this._storeNames=[],this._allTables={},this.idbdb=null,this._novip=this;const s={dbOpenError:null,isBeingOpened:!1,onReadyBeingFired:null,openComplete:!1,dbReadyResolve:Q,dbReadyPromise:null,cancelOpen:Q,openCanceller:null,autoSchema:!0,PR1398_maxLoop:3,autoOpen:t.autoOpen};var i;s.dbReadyPromise=new Oe((e=>{s.dbReadyResolve=e})),s.openCanceller=new Oe(((e,t)=>{s.cancelOpen=t})),this._state=s,this.name=e,this.on=pt(this,"populate","blocked","versionchange","close",{ready:[re,Q]}),this.on.ready.subscribe=y(this.on.ready.subscribe,(e=>(t,n)=>{Xn.vip((()=>{const r=this._state;if(r.openComplete)r.dbOpenError||Oe.resolve().then(t),n&&e(t);else if(r.onReadyBeingFired)r.onReadyBeingFired.push(t),n&&e(t);else{e(t);const r=this;n||e((function e(){r.on.ready.unsubscribe(t),r.on.ready.unsubscribe(e)}))}}))})),this.Collection=(i=this,yt(Ot.prototype,(function(e,t){this.db=i;let n=at,r=null;if(t)try{n=t()}catch(e){r=e}const s=e._ctx,o=s.table,a=o.hook.reading.fire;this._ctx={table:o,index:s.index,isPrimKey:!s.index||o.schema.primKey.keyPath&&s.index===o.schema.primKey.name,range:n,keysOnly:!1,dir:"next",unique:"",algorithm:null,filter:null,replayFilter:null,justLimit:!0,isMatch:null,offset:0,limit:1/0,error:r,or:s.or,valueMapper:a!==X?a:null}}))),this.Table=function(e){return yt(ft.prototype,(function(t,n,r){this.db=e,this._tx=r,this.name=t,this.schema=n,this.hook=e._allTables[t]?e._allTables[t].hook:pt(null,{creating:[Z,Q],reading:[H,X],updating:[te,Q],deleting:[ee,Q]})}))}(this),this.Transaction=function(e){return yt($t.prototype,(function(t,n,r,s,i){this.db=e,this.mode=t,this.storeNames=n,this.schema=r,this.chromeTransactionDurability=s,this.idbtrans=null,this.on=pt(this,"complete","error","abort"),this.parent=i||null,this.active=!0,this._reculock=0,this._blockedFuncs=[],this._resolve=null,this._reject=null,this._waitingFor=null,this._waitingQueue=null,this._spinCount=0,this._completion=new Oe(((e,t)=>{this._resolve=e,this._reject=t})),this._completion.then((()=>{this.active=!1,this.on.complete.fire()}),(e=>{var t=this.active;return this.active=!1,this.on.error.fire(e),this.parent?this.parent._reject(e):t&&this.idbtrans&&this.idbtrans.abort(),tt(e)}))}))}(this),this.Version=function(e){return yt(an.prototype,(function(t){this.db=e,this._cfg={version:t,storesSource:null,dbschema:{},tables:{},contentUpgrade:null}}))}(this),this.WhereClause=function(e){return yt(Tt.prototype,(function(t,n,r){if(this.db=e,this._ctx={table:t,index:":id"===n?null:n,or:r},this._cmp=this._ascending=ct,this._descending=(e,t)=>ct(t,e),this._max=(e,t)=>ct(e,t)>0?e:t,this._min=(e,t)=>ct(e,t)<0?e:t,this._IDBKeyRange=e._deps.IDBKeyRange,!this._IDBKeyRange)throw new W.MissingAPI}))}(this),this.on("versionchange",(e=>{e.newVersion>0?console.warn(`Another connection wants to upgrade database '${this.name}'. Closing db now to resume the upgrade.`):console.warn(`Another connection wants to delete database '${this.name}'. Closing db now to resume the delete request.`),this.close({disableAutoOpen:!1})})),this.on("blocked",(e=>{!e.newVersion||e.newVersion<e.oldVersion?console.warn(`Dexie.delete('${this.name}') was blocked`):console.warn(`Upgrade '${this.name}' blocked by other connection holding version ${e.oldVersion/10}`)})),this._maxKey=Lt(t.IDBKeyRange),this._createTransaction=(e,t,n,r)=>new this.Transaction(e,t,n,this._options.chromeTransactionDurability,r),this._fireOnBlocked=e=>{this.on("blocked").fire(e),it.filter((e=>e.name===this.name&&e!==this&&!e._state.vcFired)).map((t=>t.on("versionchange").fire(e)))},this.use($n),this.use(Gn),this.use(Mn),this.use(Dn),this.use(Bn);const o=new Proxy(this,{get:(e,t,n)=>{if("_vip"===t)return!0;if("table"===t)return e=>Qn(this.table(e),o);const r=Reflect.get(e,t,n);return r instanceof ft?Qn(r,o):"tables"===t?r.map((e=>Qn(e,o))):"_createTransaction"===t?function(){const e=r.apply(this,arguments);return Qn(e,o)}:r}});this.vip=o,r.forEach((e=>e(this)))}version(e){if(isNaN(e)||e<.1)throw new W.Type("Given version is not a positive number");if(e=Math.round(10*e)/10,this.idbdb||this._state.isBeingOpened)throw new W.Schema("Cannot add version when database is open");this.verno=Math.max(this.verno,e);const t=this._versions;var n=t.filter((t=>t._cfg.version===e))[0];return n||(n=new this.Version(e),t.push(n),t.sort(Ht),n.stores({}),this._state.autoSchema=!1,n)}_whenReady(e){return this.idbdb&&(this._state.openComplete||we.letThrough||this._vip)?e():new Oe(((e,t)=>{if(this._state.openComplete)return t(new W.DatabaseClosed(this._state.dbOpenError));if(!this._state.isBeingOpened){if(!this._state.autoOpen)return void t(new W.DatabaseClosed);this.open().catch(Q)}this._state.dbReadyPromise.then(e,t)})).then(e)}use({stack:e,create:t,level:n,name:r}){r&&this.unuse({stack:e,name:r});const s=this._middlewares[e]||(this._middlewares[e]=[]);return s.push({stack:e,create:t,level:null==n?10:n,name:r}),s.sort(((e,t)=>e.level-t.level)),this}unuse({stack:e,name:t,create:n}){return e&&this._middlewares[e]&&(this._middlewares[e]=this._middlewares[e].filter((e=>n?e.create!==n:!!t&&e.name!==t))),this}open(){return Je(ve,(()=>Sn(this)))}_close(){const e=this._state,t=it.indexOf(this);if(t>=0&&it.splice(t,1),this.idbdb){try{this.idbdb.close()}catch(e){}this.idbdb=null}e.isBeingOpened||(e.dbReadyPromise=new Oe((t=>{e.dbReadyResolve=t})),e.openCanceller=new Oe(((t,n)=>{e.cancelOpen=n})))}close({disableAutoOpen:e}={disableAutoOpen:!0}){const t=this._state;e?(t.isBeingOpened&&t.cancelOpen(new W.DatabaseClosed),this._close(),t.autoOpen=!1,t.dbOpenError=new W.DatabaseClosed):(this._close(),t.autoOpen=this._options.autoOpen||t.isBeingOpened,t.openComplete=!1,t.dbOpenError=null)}delete(e={disableAutoOpen:!0}){const t=arguments.length>0&&"object"!=typeof arguments[0],n=this._state;return new Oe(((r,s)=>{const i=()=>{this.close(e);var t=this._deps.indexedDB.deleteDatabase(this.name);t.onsuccess=Re((()=>{!function({indexedDB:e,IDBKeyRange:t},n){!ln(e)&&"__dbnames"!==n&&un(e,t).delete(n).catch(Q)}(this._deps,this.name),r()})),t.onerror=It(s),t.onblocked=this._fireOnBlocked};if(t)throw new W.InvalidArgument("Invalid closeOptions argument to db.delete()");n.isBeingOpened?n.dbReadyPromise.then(i):i()}))}backendDB(){return this.idbdb}isOpen(){return null!==this.idbdb}hasBeenClosed(){const e=this._state.dbOpenError;return e&&"DatabaseClosed"===e.name}hasFailed(){return null!==this._state.dbOpenError}dynamicallyOpened(){return this._state.autoSchema}get tables(){return t(this._allTables).map((e=>this._allTables[e]))}transaction(){const e=Cn.apply(this,arguments);return this._transaction.apply(this,e)}_transaction(e,t,n){let r=we.trans;r&&r.db===this&&-1===e.indexOf("!")||(r=null);const s=-1!==e.indexOf("?");let i,o;e=e.replace("!","").replace("?","");try{if(o=t.map((e=>{var t=e instanceof this.Table?e.name:e;if("string"!=typeof t)throw new TypeError("Invalid table argument to Dexie.transaction(). Only Table or String are allowed");return t})),"r"==e||"readonly"===e)i="readonly";else{if("rw"!=e&&"readwrite"!=e)throw new W.InvalidArgument("Invalid transaction mode: "+e);i="readwrite"}if(r){if("readonly"===r.mode&&"readwrite"===i){if(!s)throw new W.SubTransaction("Cannot enter a sub-transaction with READWRITE mode when parent transaction is READONLY");r=null}r&&o.forEach((e=>{if(r&&-1===r.storeNames.indexOf(e)){if(!s)throw new W.SubTransaction("Table "+e+" not included in parent transaction.");r=null}})),s&&r&&!r.active&&(r=null)}}catch(e){return r?r._promise(null,((t,n)=>{n(e)})):tt(e)}const a=jn.bind(null,this,i,o,r,n);return r?r._promise(i,a,"lock"):we.trans?Je(we.transless,(()=>this._whenReady(a))):this._whenReady(a)}table(e){if(!o(this._allTables,e))throw new W.InvalidTable(`Table ${e} does not exist`);return this._allTables[e]}}const Hn="undefined"!=typeof Symbol&&"observable"in Symbol?Symbol.observable:"@@observable";class Jn{constructor(e){this._subscribe=e}subscribe(e,t,n){return this._subscribe(e&&"function"!=typeof e?e:{next:e,error:t,complete:n})}[Hn](){return this}}let Zn;try{Zn={indexedDB:e.indexedDB||e.mozIndexedDB||e.webkitIndexedDB||e.msIndexedDB,IDBKeyRange:e.IDBKeyRange||e.webkitIDBKeyRange}}catch(e){Zn={indexedDB:null,IDBKeyRange:null}}function er(e){let t,n=!1;const r=new Jn((r=>{const s=B(e);let i,a=!1,u={},l={};const c={get closed(){return a},unsubscribe:()=>{a||(a=!0,i&&i.abort(),h&&Rt.storagemutated.unsubscribe(f))}};r.start&&r.start(c);let h=!1;const d=()=>et(p);const f=e=>{wn(u,e),_n(l,u)&&d()},p=()=>{if(a||!Zn.indexedDB)return;u={};const c={};i&&i.abort(),i=new AbortController;const p={subscr:c,signal:i.signal,requery:d,querier:e,trans:null},y=function(t){const n=De();try{s&&ze();let r=Ve(e,t);return s&&(r=r.finally(We)),r}finally{n&&Te()}}(p);Promise.resolve(y).then((e=>{n=!0,t=e,a||p.signal.aborted||(u={},l=c,function(e){for(const t in e)if(o(e,t))return!1;return!0}(l)||h||(Rt("storagemutated",f),h=!0),et((()=>!a&&r.next&&r.next(e))))}),(e=>{n=!1,["DatabaseClosedError","AbortError"].includes(e?.name)||a||et((()=>{a||r.error&&r.error(e)}))}))};return setTimeout(d,0),c}));return r.hasValue=()=>n,r.getValue=()=>t,r}const tr=Xn;function nr(e){let t=sr;try{sr=!0,Rt.storagemutated.fire(e),Kn(e,!0)}finally{sr=t}}a(tr,{...G,delete:e=>new tr(e,{addons:[]}).delete(),exists:e=>new tr(e,{addons:[]}).open().then((e=>(e.close(),!0))).catch("NoSuchDatabaseError",(()=>!1)),getDatabaseNames(e){try{return function({indexedDB:e,IDBKeyRange:t}){return ln(e)?Promise.resolve(e.databases()).then((e=>e.map((e=>e.name)).filter((e=>"__dbnames"!==e)))):un(e,t).toCollection().primaryKeys()}(tr.dependencies).then(e)}catch{return tt(new W.MissingAPI)}},defineClass:()=>function(e){r(this,e)},ignoreTransaction:e=>we.trans?Je(we.transless,e):e(),vip:cn,async:function(e){return function(){try{var t=An(e.apply(this,arguments));return t&&"function"==typeof t.then?t:Oe.resolve(t)}catch(e){return tt(e)}}},spawn:function(e,t,n){try{var r=An(e.apply(n,t||[]));return r&&"function"==typeof r.then?r:Oe.resolve(r)}catch(e){return tt(e)}},currentTransaction:{get:()=>we.trans||null},waitFor:function(e,t){const n=Oe.resolve("function"==typeof e?tr.ignoreTransaction(e):e).timeout(t||6e4);return we.trans?we.trans.waitFor(n):n},Promise:Oe,debug:{get:()=>se,set:e=>{ie(e)}},derive:c,extend:r,props:a,override:y,Events:pt,on:Rt,liveQuery:er,extendObservabilitySet:wn,getByKeyPath:g,setByKeyPath:v,delByKeyPath:function(e,t){"string"==typeof t?v(e,t,void 0):"length"in t&&[].map.call(t,(function(t){v(e,t,void 0)}))},shallowClone:w,deepClone:E,getObjectDiff:Tn,cmp:ct,asap:b,minKey:-(1/0),addons:[],connections:it,errnames:V,dependencies:Zn,cache:xn,semVer:"4.0.11",version:"4.0.11".split(".").map((e=>parseInt(e))).reduce(((e,t,n)=>e+t/Math.pow(10,2*n)))}),tr.maxKey=Lt(tr.dependencies.IDBKeyRange),"undefined"!=typeof dispatchEvent&&"undefined"!=typeof addEventListener&&(Rt("storagemutated",(e=>{if(!sr){let t;t=new CustomEvent("x-storagemutated-1",{detail:e}),sr=!0,dispatchEvent(t),sr=!1}})),addEventListener("x-storagemutated-1",(({detail:e})=>{sr||nr(e)})));let rr,sr=!1,ir=()=>{};function or(e){return new kt({add:e})}function ar(e){return new kt({remove:e})}function ur(e,t){return new kt({replacePrefix:[e,t]})}"undefined"!=typeof BroadcastChannel&&(ir=()=>{rr=new BroadcastChannel("x-storagemutated-1"),rr.onmessage=e=>e.data&&nr(e.data)},ir(),"function"==typeof rr.unref&&rr.unref(),Rt("storagemutated",(e=>{sr||rr.postMessage(e)}))),"undefined"!=typeof addEventListener&&(addEventListener("pagehide",(e=>{if(!Xn.disableBfCache&&e.persisted){se&&console.debug("Dexie: handling persisted pagehide"),rr?.close();for(const e of it)e.close({disableAutoOpen:!1})}})),addEventListener("pageshow",(e=>{!Xn.disableBfCache&&e.persisted&&(se&&console.debug("Dexie: handling persisted pageshow"),ir(),nr({all:new fn(-1/0,[[]])}))}))),Oe.rejectionMapper=function(e,t){if(!e||e instanceof F||e instanceof TypeError||e instanceof SyntaxError||!e.name||!Y[e.name])return e;var n=new Y[e.name](t||e.message,e);return"stack"in e&&l(n,"stack",{get:function(){return this.inner.stack}}),n},ie(se);export{Xn as Dexie,lt as Entity,kt as PropModification,fn as RangeSet,or as add,ct as cmp,Xn as default,er as liveQuery,yn as mergeRanges,mn as rangesOverlap,ar as remove,ur as replacePrefix};
//# sourceMappingURL=dexie.min.mjs.map