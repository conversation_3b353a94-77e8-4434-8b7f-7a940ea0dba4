
import React, { useState, useRef, useEffect } from "react";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Pencil, Plus, Trash2, UploadCloud, Image as ImageIcon, X } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { getCategories, saveCategories, Category } from "@/services/categoryService";
import { fixCategoryImages } from "@/services/imageFixService";

const Categories = () => {
  const { toast } = useToast();
  const [categories, setCategories] = useState<Category[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);
  const [newCategoryName, setNewCategoryName] = useState("");
  const [newCategoryDescription, setNewCategoryDescription] = useState("");
  const [newCategoryOrder, setNewCategoryOrder] = useState<number>(0);
  const [newCategoryShowInHomepage, setNewCategoryShowInHomepage] = useState(true);
  const [newCategorySlug, setNewCategorySlug] = useState("");
  const [newCategorySeoTitle, setNewCategorySeoTitle] = useState("");
  const [newCategorySeoDescription, setNewCategorySeoDescription] = useState("");

  const [activeTab, setActiveTab] = useState("active");
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [editSelectedImage, setEditSelectedImage] = useState<string | null>(null);

  // حالة تبويب الإعدادات المتقدمة
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);
  const editFileInputRef = useRef<HTMLInputElement>(null);

  // جلب الأقسام من التخزين المحلي عند تحميل الصفحة
  useEffect(() => {
    // إصلاح صور الأقسام أولاً
    fixCategoryImages();

    const storedCategories = getCategories();
    setCategories(storedCategories);
  }, []);

  const filteredCategories = activeTab === "active"
    ? categories.filter(cat => cat.active)
    : categories.filter(cat => !cat.active);

  const handleAddCategory = () => {
    console.log("handleAddCategory called");
    console.log("newCategoryName:", newCategoryName);
    console.log("newCategoryDescription:", newCategoryDescription);
    console.log("selectedImage:", selectedImage);

    if (!newCategoryName.trim()) {
      console.log("Error: Category name is empty");
      toast({
        title: "خطأ",
        description: "يرجى إدخال اسم القسم",
        variant: "destructive",
      });
      return;
    }

    // إنشاء slug تلقائي إذا لم يتم إدخاله
    const slug = newCategorySlug.trim() || newCategoryName
      .trim()
      .toLowerCase()
      .replace(/\s+/g, '-') // استبدال المسافات بشرطات
      .replace(/[^\w\u0621-\u064A-]/g, '') // إزالة الأحرف الخاصة مع الاحتفاظ بالأحرف العربية
      .replace(/--+/g, '-'); // استبدال الشرطات المتعددة بشرطة واحدة

    // إنشاء عنوان SEO تلقائي إذا لم يتم إدخاله
    const seoTitle = newCategorySeoTitle.trim() || newCategoryName.trim();

    // إنشاء وصف SEO تلقائي إذا لم يتم إدخاله
    const seoDescription = newCategorySeoDescription.trim() ||
      (newCategoryDescription.trim() ? newCategoryDescription.trim() : `تسوق منتجات ${newCategoryName} بأفضل الأسعار`);

    // الحصول على أعلى ترتيب موجود وإضافة 1 إليه
    const maxOrder = categories.length > 0
      ? Math.max(...categories.map(c => c.order || 0))
      : 0;
    const order = newCategoryOrder > 0 ? newCategoryOrder : maxOrder + 1;

    const newCategory = {
      id: categories.length > 0 ? Math.max(...categories.map(c => c.id)) + 1 : 1,
      name: newCategoryName,
      description: newCategoryDescription,
      imageUrl: selectedImage || `https://placehold.co/200x200/3b82f6/ffffff?text=${encodeURIComponent(newCategoryName)}`,
      productsCount: 0,
      active: true,
      order: order,
      showInHomepage: newCategoryShowInHomepage,
      slug: slug,
      seoTitle: seoTitle,
      seoDescription: seoDescription
    };

    console.log("Creating new category:", newCategory);

    const updatedCategories = [...categories, newCategory];
    console.log("Updated categories:", updatedCategories);

    setCategories(updatedCategories);

    // حفظ الأقسام في التخزين المحلي
    try {
      saveCategories(updatedCategories);
      console.log("Categories saved successfully");
    } catch (error) {
      console.error("Error saving categories:", error);
      toast({
        title: "خطأ",
        description: "حدث خطأ أثناء حفظ القسم",
        variant: "destructive",
      });
      return;
    }

    // إعادة تعيين الحقول
    setNewCategoryName("");
    setNewCategoryDescription("");
    setNewCategoryOrder(0);
    setNewCategoryShowInHomepage(true);
    setNewCategorySlug("");
    setNewCategorySeoTitle("");
    setNewCategorySeoDescription("");
    setSelectedImage(null);
    setShowAdvancedSettings(false);
    setIsAddDialogOpen(false);

    console.log("Category added successfully");
    toast({
      title: "تم بنجاح",
      description: "تم إضافة القسم الجديد",
    });
  };

  const handleEditCategory = () => {
    if (!selectedCategory || !newCategoryName.trim()) {
      toast({
        title: "خطأ",
        description: "يرجى إدخال اسم القسم",
        variant: "destructive",
      });
      return;
    }

    // إنشاء slug تلقائي إذا لم يتم إدخاله
    const slug = newCategorySlug.trim() || newCategoryName
      .trim()
      .toLowerCase()
      .replace(/\s+/g, '-') // استبدال المسافات بشرطات
      .replace(/[^\w\u0621-\u064A-]/g, '') // إزالة الأحرف الخاصة مع الاحتفاظ بالأحرف العربية
      .replace(/--+/g, '-'); // استبدال الشرطات المتعددة بشرطة واحدة

    // إنشاء عنوان SEO تلقائي إذا لم يتم إدخاله
    const seoTitle = newCategorySeoTitle.trim() || newCategoryName.trim();

    // إنشاء وصف SEO تلقائي إذا لم يتم إدخاله
    const seoDescription = newCategorySeoDescription.trim() ||
      (newCategoryDescription.trim() ? newCategoryDescription.trim() : `تسوق منتجات ${newCategoryName} بأفضل الأسعار`);

    // تحديد الصورة الجديدة
    let newImageUrl = selectedCategory.imageUrl; // الصورة الحالية كافتراضي

    if (editSelectedImage === null) {
      // إذا تم حذف الصورة، استخدم صورة افتراضية
      newImageUrl = `https://placehold.co/200x200/3b82f6/ffffff?text=${encodeURIComponent(newCategoryName)}`;
    } else if (editSelectedImage && editSelectedImage !== selectedCategory.imageUrl) {
      // إذا تم اختيار صورة جديدة
      newImageUrl = editSelectedImage;
    }

    const updatedCategories = categories.map(cat =>
      cat.id === selectedCategory.id
        ? {
            ...cat,
            name: newCategoryName,
            description: newCategoryDescription,
            imageUrl: newImageUrl,
            order: newCategoryOrder > 0 ? newCategoryOrder : cat.order,
            showInHomepage: newCategoryShowInHomepage,
            slug: slug,
            seoTitle: seoTitle,
            seoDescription: seoDescription
          }
        : cat
    );

    setCategories(updatedCategories);

    // حفظ الأقسام في التخزين المحلي
    saveCategories(updatedCategories);

    // إعادة تعيين الحقول
    setNewCategoryName("");
    setNewCategoryDescription("");
    setNewCategoryOrder(0);
    setNewCategoryShowInHomepage(true);
    setNewCategorySlug("");
    setNewCategorySeoTitle("");
    setNewCategorySeoDescription("");
    setEditSelectedImage(null);
    setShowAdvancedSettings(false);
    setIsEditDialogOpen(false);
    setSelectedCategory(null);

    toast({
      title: "تم بنجاح",
      description: "تم تعديل القسم",
    });
  };

  const handleDeleteCategory = (id: number) => {
    // In a real app, you might want a confirmation dialog here
    const updatedCategories = categories.map(cat =>
      cat.id === id
        ? { ...cat, active: false }
        : cat
    );

    setCategories(updatedCategories);

    // حفظ الأقسام في التخزين المحلي
    saveCategories(updatedCategories);

    toast({
      title: "تم بنجاح",
      description: "تم إلغاء تنشيط القسم",
    });
  };

  // فتح نافذة تأكيد الحذف
  const openDeleteDialog = (category: Category) => {
    setCategoryToDelete(category);
    setIsDeleteDialogOpen(true);
  };

  // حذف القسم نهائياً
  const handlePermanentDeleteCategory = () => {
    if (categoryToDelete) {
      const updatedCategories = categories.filter(cat => cat.id !== categoryToDelete.id);
      setCategories(updatedCategories);

      // حفظ الأقسام في التخزين المحلي
      saveCategories(updatedCategories);

      toast({
        title: "تم بنجاح",
        description: `تم حذف قسم "${categoryToDelete.name}" نهائياً`,
      });

      // إغلاق النافذة وإعادة تعيين البيانات
      setIsDeleteDialogOpen(false);
      setCategoryToDelete(null);
    }
  };

  // إلغاء الحذف
  const cancelDelete = () => {
    setIsDeleteDialogOpen(false);
    setCategoryToDelete(null);
  };

  const handleActivateCategory = (id: number) => {
    const updatedCategories = categories.map(cat =>
      cat.id === id
        ? { ...cat, active: true }
        : cat
    );

    setCategories(updatedCategories);

    // حفظ الأقسام في التخزين المحلي
    saveCategories(updatedCategories);

    toast({
      title: "تم بنجاح",
      description: "تم تنشيط القسم",
    });
  };

  const openEditDialog = (category: Category) => {
    setSelectedCategory(category);
    setNewCategoryName(category.name);
    setNewCategoryDescription(category.description || "");
    setNewCategoryOrder(category.order || 0);
    setNewCategoryShowInHomepage(category.showInHomepage || false);
    setNewCategorySlug(category.slug || "");
    setNewCategorySeoTitle(category.seoTitle || "");
    setNewCategorySeoDescription(category.seoDescription || "");
    setEditSelectedImage(category.imageUrl);
    setIsEditDialogOpen(true);
  };

  // Handle file selection for new category
  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;

    if (files && files.length > 0) {
      console.log("Files selected:", files.length);

      const file = files[0]; // Take only the first file
      console.log("Processing file:", file.name);

      // Create a FileReader to read the image
      const reader = new FileReader();

      reader.onload = (event) => {
        if (event.target && event.target.result) {
          console.log("File loaded successfully");
          setSelectedImage(event.target.result as string);
        }
      };

      reader.readAsDataURL(file);

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } else {
      console.log("No files selected");
    }
  };

  // Handle file selection for edit category
  const handleEditFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;

    if (files && files.length > 0) {
      console.log("Edit Files selected:", files.length);

      const file = files[0]; // Take only the first file
      console.log("Processing edit file:", file.name);

      // Create a FileReader to read the image
      const reader = new FileReader();

      reader.onload = (event) => {
        if (event.target && event.target.result) {
          console.log("Edit file loaded successfully");
          setEditSelectedImage(event.target.result as string);
        }
      };

      reader.readAsDataURL(file);

      // Reset the file input
      if (editFileInputRef.current) {
        editFileInputRef.current.value = "";
      }
    } else {
      console.log("No edit files selected");
    }
  };

  const handleImageUploadClick = () => {
    console.log("Upload button clicked");
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleEditImageUploadClick = () => {
    console.log("Edit upload button clicked");
    if (editFileInputRef.current) {
      editFileInputRef.current.click();
    }
  };

  const clearSelectedImage = () => {
    setSelectedImage(null);
  };

  const clearEditSelectedImage = () => {
    console.log("Clearing edit selected image");
    setEditSelectedImage(null);
  };

  return (
    <DashboardLayout>
      <div className="flex flex-col space-y-8">
        {/* Page header */}
        <div className="flex flex-col md:flex-row md:items-center md:justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">أقسام المنتجات</h2>
            <p className="text-muted-foreground mt-2">
              إدارة أقسام المنتجات في المتجر
            </p>
          </div>
          <Button onClick={() => setIsAddDialogOpen(true)} className="mt-4 md:mt-0">
            <Plus className="ml-2 h-4 w-4" />
            إضافة قسم جديد
          </Button>
        </div>

        <Card>
          <CardHeader>
            <Tabs defaultValue="active" className="w-full" onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="active">الأقسام النشطة</TabsTrigger>
                <TabsTrigger value="inactive">الأقسام غير النشطة</TabsTrigger>
              </TabsList>
            </Tabs>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCategories.map(category => (
                <Card key={category.id} className="overflow-hidden hover:shadow-card-hover transition-shadow">
                  <div className="relative aspect-square overflow-hidden bg-gray-100">
                    <img
                      src={category.imageUrl}
                      alt={category.name}
                      className="object-cover w-full h-full"
                    />
                    <div className="absolute top-2 right-2 bg-white bg-opacity-80 px-2 py-1 rounded text-xs">
                      {category.productsCount} منتج
                    </div>
                    {category.showInHomepage && (
                      <div className="absolute bottom-2 left-2 bg-green-500 text-white px-2 py-1 rounded-full text-xs">
                        الصفحة الرئيسية
                      </div>
                    )}
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-medium text-lg">{category.name}</h3>
                    {category.description && (
                      <p className="text-sm text-gray-500 mt-1 line-clamp-2">{category.description}</p>
                    )}
                    {category.order > 0 && (
                      <div className="mt-2 text-xs text-gray-400">
                        الترتيب: {category.order}
                      </div>
                    )}
                  </CardContent>
                  <CardFooter className="flex justify-between p-4 pt-0">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => openEditDialog(category)}
                    >
                      <Pencil className="h-4 w-4 ml-1" />
                      تعديل
                    </Button>
                    <div className="flex gap-2">
                      {activeTab === "active" ? (
                        <>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeleteCategory(category.id)}
                          >
                            <Trash2 className="h-4 w-4 ml-1" />
                            إلغاء تنشيط
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => openDeleteDialog(category)}
                            className="bg-red-600 hover:bg-red-700"
                          >
                            <X className="h-4 w-4 ml-1" />
                            حذف نهائي
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button
                            variant="default"
                            size="sm"
                            onClick={() => handleActivateCategory(category.id)}
                          >
                            تنشيط
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => openDeleteDialog(category)}
                            className="bg-red-600 hover:bg-red-700"
                          >
                            <X className="h-4 w-4 ml-1" />
                            حذف نهائي
                          </Button>
                        </>
                      )}
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>

            {filteredCategories.length === 0 && (
              <div className="text-center py-12">
                <div className="flex justify-center">
                  <ImageIcon className="h-12 w-12 text-gray-300" />
                </div>
                <h3 className="mt-4 text-lg font-medium text-gray-900">لا توجد أقسام</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {activeTab === "active"
                    ? "لم يتم إضافة أي أقسام نشطة بعد. يمكنك إضافة قسم جديد بالضغط على زر \"إضافة قسم جديد\""
                    : "لا توجد أقسام غير نشطة حالياً"}
                </p>
                {activeTab === "active" && (
                  <div className="mt-6">
                    <Button onClick={() => setIsAddDialogOpen(true)}>
                      <Plus className="ml-2 h-4 w-4" />
                      إضافة قسم جديد
                    </Button>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Add Category Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>إضافة قسم جديد</DialogTitle>
            <DialogDescription>
              أدخل تفاصيل القسم الجديد أدناه. اختر اسماً واضحاً ومعبراً.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4 max-h-[60vh] overflow-y-auto">
            <div className="space-y-2">
              <Label htmlFor="name">اسم القسم</Label>
              <Input
                id="name"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
                placeholder="مثال: ملابس رجالية"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">وصف القسم</Label>
              <textarea
                id="description"
                className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={newCategoryDescription}
                onChange={(e) => setNewCategoryDescription(e.target.value)}
                placeholder="أدخل وصفًا مختصرًا للقسم"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="image">صورة القسم</Label>
              {selectedImage ? (
                <div className="space-y-3">
                  <div className="relative">
                    <img
                      src={selectedImage}
                      alt="Selected category"
                      className="w-full object-cover rounded-md aspect-square"
                    />
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2 rounded-full h-8 w-8 shadow-lg"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        clearSelectedImage();
                      }}
                      type="button"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleImageUploadClick();
                      }}
                      type="button"
                      className="flex-1"
                    >
                      <UploadCloud className="h-4 w-4 ml-2" />
                      تغيير الصورة
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        clearSelectedImage();
                      }}
                      type="button"
                      className="flex-1"
                    >
                      <X className="h-4 w-4 ml-2" />
                      حذف الصورة
                    </Button>
                  </div>
                </div>
              ) : (
                <div
                  className="border border-dashed rounded-md p-6 flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={handleImageUploadClick}
                >
                  <UploadCloud className="h-10 w-10 text-gray-300 mb-2" />
                  <p className="text-sm text-gray-500">اضغط أو اسحب الصورة هنا</p>
                  <p className="text-xs text-gray-400 mt-1">PNG, JPG (max. 2MB)</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleImageUploadClick();
                    }}
                    type="button"
                  >
                    <UploadCloud className="h-4 w-4 ml-2" />
                    اختيار صورة
                  </Button>
                </div>
              )}
              <input
                type="file"
                accept="image/*"
                ref={fileInputRef}
                className="hidden"
                onChange={handleFileSelect}
              />
            </div>

            <div className="flex items-center space-x-2 space-x-reverse">
              <Checkbox
                id="showInHomepage"
                checked={newCategoryShowInHomepage}
                onCheckedChange={(checked) => setNewCategoryShowInHomepage(checked as boolean)}
              />
              <Label htmlFor="showInHomepage">عرض في الصفحة الرئيسية</Label>
            </div>

            <div className="space-y-2">
              <Label htmlFor="order">ترتيب العرض</Label>
              <Input
                id="order"
                type="number"
                min="0"
                value={newCategoryOrder || ""}
                onChange={(e) => setNewCategoryOrder(parseInt(e.target.value) || 0)}
                placeholder="أدخل رقم الترتيب (0 للترتيب التلقائي)"
              />
              <p className="text-xs text-muted-foreground">الرقم الأصغر يظهر أولاً. اترك 0 للترتيب التلقائي.</p>
            </div>

            <div className="pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
                className="w-full"
              >
                {showAdvancedSettings ? "إخفاء الإعدادات المتقدمة" : "عرض الإعدادات المتقدمة"}
              </Button>
            </div>

            {showAdvancedSettings && (
              <div className="space-y-4 pt-4 border-t">
                <div className="space-y-2">
                  <Label htmlFor="slug">الرابط المخصص</Label>
                  <div className="flex items-center">
                    <span className="text-sm text-muted-foreground px-3 border rounded-r-none rounded-l-md border-r-0 h-10 flex items-center bg-muted">/category/</span>
                    <Input
                      id="slug"
                      value={newCategorySlug}
                      onChange={(e) => setNewCategorySlug(e.target.value)}
                      placeholder="مثال: mens-clothing"
                      className="rounded-r-md rounded-l-none"
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">سيتم إنشاء الرابط تلقائيًا إذا تركته فارغًا.</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="seoTitle">عنوان SEO</Label>
                  <Input
                    id="seoTitle"
                    value={newCategorySeoTitle}
                    onChange={(e) => setNewCategorySeoTitle(e.target.value)}
                    placeholder="عنوان الصفحة في نتائج البحث"
                  />
                  <p className="text-xs text-muted-foreground">سيتم استخدام اسم القسم إذا تركته فارغًا.</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="seoDescription">وصف SEO</Label>
                  <textarea
                    id="seoDescription"
                    className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={newCategorySeoDescription}
                    onChange={(e) => setNewCategorySeoDescription(e.target.value)}
                    placeholder="وصف الصفحة في نتائج البحث"
                  />
                  <p className="text-xs text-muted-foreground">سيتم استخدام وصف القسم إذا تركته فارغًا.</p>
                </div>
              </div>
            )}
          </div>
          <DialogFooter className="sticky bottom-0 bg-white border-t pt-4 mt-4">
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>إلغاء</Button>
            <Button
              onClick={() => {
                console.log("Add category button clicked");
                handleAddCategory();
              }}
              disabled={!newCategoryName.trim()}
            >
              إضافة القسم
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Category Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>تعديل القسم</DialogTitle>
            <DialogDescription>
              قم بتعديل تفاصيل القسم أدناه.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4 max-h-[60vh] overflow-y-auto">
            <div className="space-y-2">
              <Label htmlFor="edit-name">اسم القسم</Label>
              <Input
                id="edit-name"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-description">وصف القسم</Label>
              <textarea
                id="edit-description"
                className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                value={newCategoryDescription}
                onChange={(e) => setNewCategoryDescription(e.target.value)}
                placeholder="أدخل وصفًا مختصرًا للقسم"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-image">صورة القسم</Label>
              {editSelectedImage ? (
                <div className="space-y-3">
                  <div className="relative">
                    <img
                      src={editSelectedImage}
                      alt={selectedCategory?.name || "Category"}
                      className="w-full object-cover rounded-md aspect-square"
                    />
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2 rounded-full h-8 w-8 shadow-lg"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        clearEditSelectedImage();
                      }}
                      type="button"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleEditImageUploadClick();
                      }}
                      type="button"
                      className="flex-1"
                    >
                      <UploadCloud className="h-4 w-4 ml-2" />
                      تغيير الصورة
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        clearEditSelectedImage();
                      }}
                      type="button"
                      className="flex-1"
                    >
                      <X className="h-4 w-4 ml-2" />
                      حذف الصورة
                    </Button>
                  </div>
                </div>
              ) : (
                <div
                  className="border border-dashed rounded-md p-6 flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={handleEditImageUploadClick}
                >
                  <UploadCloud className="h-10 w-10 text-gray-300 mb-2" />
                  <p className="text-sm text-gray-500">اضغط أو اسحب الصورة هنا لتغييرها</p>
                  <p className="text-xs text-gray-400 mt-1">PNG, JPG (max. 2MB)</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleEditImageUploadClick();
                    }}
                    type="button"
                  >
                    <UploadCloud className="h-4 w-4 ml-2" />
                    اختيار صورة
                  </Button>
                </div>
              )}
              <input
                type="file"
                accept="image/*"
                ref={editFileInputRef}
                className="hidden"
                onChange={handleEditFileSelect}
              />
            </div>

            <div className="flex items-center space-x-2 space-x-reverse">
              <Checkbox
                id="edit-showInHomepage"
                checked={newCategoryShowInHomepage}
                onCheckedChange={(checked) => setNewCategoryShowInHomepage(checked as boolean)}
              />
              <Label htmlFor="edit-showInHomepage">عرض في الصفحة الرئيسية</Label>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-order">ترتيب العرض</Label>
              <Input
                id="edit-order"
                type="number"
                min="0"
                value={newCategoryOrder || ""}
                onChange={(e) => setNewCategoryOrder(parseInt(e.target.value) || 0)}
                placeholder="أدخل رقم الترتيب (0 للترتيب التلقائي)"
              />
              <p className="text-xs text-muted-foreground">الرقم الأصغر يظهر أولاً. اترك 0 للترتيب التلقائي.</p>
            </div>

            <div className="pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowAdvancedSettings(!showAdvancedSettings)}
                className="w-full"
              >
                {showAdvancedSettings ? "إخفاء الإعدادات المتقدمة" : "عرض الإعدادات المتقدمة"}
              </Button>
            </div>

            {showAdvancedSettings && (
              <div className="space-y-4 pt-4 border-t">
                <div className="space-y-2">
                  <Label htmlFor="edit-slug">الرابط المخصص</Label>
                  <div className="flex items-center">
                    <span className="text-sm text-muted-foreground px-3 border rounded-r-none rounded-l-md border-r-0 h-10 flex items-center bg-muted">/category/</span>
                    <Input
                      id="edit-slug"
                      value={newCategorySlug}
                      onChange={(e) => setNewCategorySlug(e.target.value)}
                      placeholder="مثال: mens-clothing"
                      className="rounded-r-md rounded-l-none"
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">سيتم إنشاء الرابط تلقائيًا إذا تركته فارغًا.</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-seoTitle">عنوان SEO</Label>
                  <Input
                    id="edit-seoTitle"
                    value={newCategorySeoTitle}
                    onChange={(e) => setNewCategorySeoTitle(e.target.value)}
                    placeholder="عنوان الصفحة في نتائج البحث"
                  />
                  <p className="text-xs text-muted-foreground">سيتم استخدام اسم القسم إذا تركته فارغًا.</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-seoDescription">وصف SEO</Label>
                  <textarea
                    id="edit-seoDescription"
                    className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={newCategorySeoDescription}
                    onChange={(e) => setNewCategorySeoDescription(e.target.value)}
                    placeholder="وصف الصفحة في نتائج البحث"
                  />
                  <p className="text-xs text-muted-foreground">سيتم استخدام وصف القسم إذا تركته فارغًا.</p>
                </div>
              </div>
            )}
          </div>
          <DialogFooter className="sticky bottom-0 bg-white border-t pt-4 mt-4">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>إلغاء</Button>
            <Button onClick={handleEditCategory}>حفظ التغييرات</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-3 text-red-600">
              <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
                <X className="h-6 w-6 text-red-600" />
              </div>
              تأكيد الحذف
            </DialogTitle>
            <DialogDescription className="text-base pt-4">
              هل أنت متأكد من حذف قسم <span className="font-bold text-gray-900">"{categoryToDelete?.name}"</span> نهائياً؟
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <div className="h-5 w-5 rounded-full bg-red-500 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-white text-xs font-bold">!</span>
                </div>
                <div className="text-sm text-red-800">
                  <p className="font-medium mb-1">تحذير مهم:</p>
                  <ul className="space-y-1 text-red-700">
                    <li>• سيتم حذف القسم نهائياً من النظام</li>
                    <li>• لا يمكن التراجع عن هذا الإجراء</li>
                    <li>• قد يؤثر على المنتجات المرتبطة بهذا القسم</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="gap-3">
            <Button
              variant="outline"
              onClick={cancelDelete}
              className="flex-1"
            >
              <span className="ml-2">✕</span>
              إلغاء
            </Button>
            <Button
              variant="destructive"
              onClick={handlePermanentDeleteCategory}
              className="flex-1 bg-red-600 hover:bg-red-700"
            >
              <span className="ml-2">🗑️</span>
              نعم، احذف نهائياً
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
};

export default Categories;
