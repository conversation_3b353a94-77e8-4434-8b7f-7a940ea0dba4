import { Marketer } from "@/pages/Marketers";

// نوع بيانات العمولة
export interface Commission {
  id: string;
  marketerId: string;
  orderId: string;
  orderNumber: string;
  amount: number;
  status: "pending" | "approved" | "paid" | "cancelled";
  createdAt: string;
  updatedAt: string;
}

// نوع بيانات عملية السحب
export interface Withdrawal {
  id: string;
  marketerId: string;
  amount: number;
  method: "bank" | "cash" | "wallet";
  status: "pending" | "completed" | "cancelled";
  notes: string;
  createdAt: string;
  updatedAt: string;
}

// مفاتيح التخزين المحلي
const STORAGE_KEYS = {
  MARKETERS: "marketers", // استخدام نفس المفتاح الذي تستخدمه صفحة المسوقين
  COMMISSIONS: "commissions",
  WITHDRAWALS: "withdrawals",
};

// بيانات تجريبية للمسوقين
const mockMarketers: Marketer[] = [
  {
    id: "m1",
    name: "محمد علي",
    phone: "***********",
    email: "<EMAIL>",
    status: "active",
    totalCommission: 5200,
    pendingCommission: 1200,
    withdrawnCommission: 4000,
    ordersCount: 42,
    createdAt: "2023-01-15T10:30:00Z",
    updatedAt: "2023-05-20T14:30:00Z",
    commissionRate: 12,
  },
  {
    id: "m2",
    name: "فاطمة حسن",
    phone: "***********",
    email: "<EMAIL>",
    status: "active",
    totalCommission: 3800,
    pendingCommission: 800,
    withdrawnCommission: 3000,
    ordersCount: 31,
    createdAt: "2023-02-20T14:45:00Z",
    updatedAt: "2023-05-15T09:30:00Z",
    commissionRate: 10,
  },
  {
    id: "m3",
    name: "أحمد محمود",
    phone: "01212345678",
    email: "<EMAIL>",
    status: "inactive",
    totalCommission: 1500,
    pendingCommission: 0,
    withdrawnCommission: 1500,
    ordersCount: 12,
    createdAt: "2023-03-10T09:15:00Z",
    updatedAt: "2023-04-05T11:20:00Z",
    commissionRate: 8,
  },
  {
    id: "m4",
    name: "سارة خالد",
    phone: "01512345678",
    email: "<EMAIL>",
    status: "active",
    totalCommission: 7200,
    pendingCommission: 2200,
    withdrawnCommission: 5000,
    ordersCount: 58,
    createdAt: "2023-01-05T11:20:00Z",
    updatedAt: "2023-05-22T16:45:00Z",
    commissionRate: 15,
  },
  {
    id: "m5",
    name: "خالد عبد الله",
    phone: "01612345678",
    email: "<EMAIL>",
    status: "active",
    totalCommission: 4300,
    pendingCommission: 1300,
    withdrawnCommission: 3000,
    ordersCount: 35,
    createdAt: "2023-02-12T16:30:00Z",
    updatedAt: "2023-04-18T10:15:00Z",
    commissionRate: 10,
  },
];

// بيانات تجريبية للعمولات
const mockCommissions: Commission[] = [
  {
    id: "c1",
    marketerId: "m1",
    orderId: "o1",
    orderNumber: "ORD-001",
    amount: 120,
    status: "approved",
    createdAt: "2023-05-10T14:30:00Z",
    updatedAt: "2023-05-10T14:30:00Z",
  },
  {
    id: "c2",
    marketerId: "m1",
    orderId: "o2",
    orderNumber: "ORD-002",
    amount: 80,
    status: "pending",
    createdAt: "2023-05-12T10:15:00Z",
    updatedAt: "2023-05-12T10:15:00Z",
  },
  {
    id: "c3",
    marketerId: "m2",
    orderId: "o3",
    orderNumber: "ORD-003",
    amount: 150,
    status: "approved",
    createdAt: "2023-05-11T09:45:00Z",
    updatedAt: "2023-05-11T09:45:00Z",
  },
  {
    id: "c4",
    marketerId: "m2",
    orderId: "o4",
    orderNumber: "ORD-004",
    amount: 100,
    status: "paid",
    createdAt: "2023-05-09T16:20:00Z",
    updatedAt: "2023-05-09T16:20:00Z",
  },
  {
    id: "c5",
    marketerId: "m3",
    orderId: "o5",
    orderNumber: "ORD-005",
    amount: 90,
    status: "cancelled",
    createdAt: "2023-05-08T11:30:00Z",
    updatedAt: "2023-05-08T11:30:00Z",
  },
];

// بيانات تجريبية لعمليات السحب
const mockWithdrawals: Withdrawal[] = [
  {
    id: "w1",
    marketerId: "m1",
    amount: 500,
    method: "bank",
    status: "completed",
    notes: "تحويل بنكي إلى حساب CIB",
    createdAt: "2023-05-15T10:30:00Z",
    updatedAt: "2023-05-15T10:30:00Z",
  },
  {
    id: "w2",
    marketerId: "m2",
    amount: 300,
    method: "cash",
    status: "completed",
    notes: "تسليم نقدي",
    createdAt: "2023-05-14T14:45:00Z",
    updatedAt: "2023-05-14T14:45:00Z",
  },
  {
    id: "w3",
    marketerId: "m1",
    amount: 200,
    method: "wallet",
    status: "pending",
    notes: "تحويل إلى محفظة فودافون كاش",
    createdAt: "2023-05-16T09:15:00Z",
    updatedAt: "2023-05-16T09:15:00Z",
  },
];

// تهيئة البيانات في التخزين المحلي
export const initializeMarketerData = (): void => {
  if (!localStorage.getItem(STORAGE_KEYS.MARKETERS)) {
    localStorage.setItem(STORAGE_KEYS.MARKETERS, JSON.stringify(mockMarketers));
  }

  if (!localStorage.getItem(STORAGE_KEYS.COMMISSIONS)) {
    localStorage.setItem(STORAGE_KEYS.COMMISSIONS, JSON.stringify(mockCommissions));
  }

  if (!localStorage.getItem(STORAGE_KEYS.WITHDRAWALS)) {
    localStorage.setItem(STORAGE_KEYS.WITHDRAWALS, JSON.stringify(mockWithdrawals));
  }
};

// الحصول على جميع المسوقين
export const getMarketers = (): Marketer[] => {
  const storedMarketers = localStorage.getItem(STORAGE_KEYS.MARKETERS);
  if (storedMarketers) {
    return JSON.parse(storedMarketers);
  }
  return [];
};

// الحصول على مسوق محدد
export const getMarketerById = (id: string): Marketer | null => {
  const marketers = getMarketers();
  return marketers.find((marketer) => marketer.id === id) || null;
};

// إضافة مسوق جديد
export const addMarketer = (marketer: Omit<Marketer, "id" | "createdAt" | "updatedAt" | "totalCommission" | "pendingCommission" | "withdrawnCommission" | "ordersCount">): Marketer => {
  const marketers = getMarketers();
  const now = new Date().toISOString();
  const newMarketer: Marketer = {
    id: `m${Date.now()}`,
    createdAt: now,
    updatedAt: now,
    totalCommission: 0,
    pendingCommission: 0,
    withdrawnCommission: 0,
    ordersCount: 0,
    commissionRate: 10, // نسبة العمولة الافتراضية 10%
    ...marketer,
  };

  marketers.push(newMarketer);
  localStorage.setItem(STORAGE_KEYS.MARKETERS, JSON.stringify(marketers));
  return newMarketer;
};

// تحديث مسوق
export const updateMarketer = (updatedMarketer: Marketer): Marketer => {
  const marketers = getMarketers();
  const index = marketers.findIndex((marketer) => marketer.id === updatedMarketer.id);

  if (index !== -1) {
    // تحديث تاريخ التحديث إذا لم يتم تحديده
    if (!updatedMarketer.updatedAt) {
      updatedMarketer.updatedAt = new Date().toISOString();
    }

    marketers[index] = updatedMarketer;
    localStorage.setItem(STORAGE_KEYS.MARKETERS, JSON.stringify(marketers));
    return updatedMarketer;
  }

  throw new Error("المسوق غير موجود");
};

// حذف مسوق
export const deleteMarketer = (id: string): void => {
  const marketers = getMarketers();
  const filteredMarketers = marketers.filter((marketer) => marketer.id !== id);
  localStorage.setItem(STORAGE_KEYS.MARKETERS, JSON.stringify(filteredMarketers));
};

// الحصول على جميع العمولات
export const getCommissions = (): Commission[] => {
  const storedCommissions = localStorage.getItem(STORAGE_KEYS.COMMISSIONS);
  if (storedCommissions) {
    return JSON.parse(storedCommissions);
  }
  return [];
};

// الحصول على عمولات مسوق محدد
export const getCommissionsByMarketerId = (marketerId: string): Commission[] => {
  const commissions = getCommissions();
  return commissions.filter((commission) => commission.marketerId === marketerId);
};

// إضافة عمولة جديدة
export const addCommission = (
  commission: Omit<Commission, "id" | "createdAt" | "updatedAt">,
  incrementOrderCount: boolean = true
): Commission => {
  // التحقق من صحة البيانات
  if (!commission.marketerId) {
    throw new Error("معرف المسوق غير محدد");
  }

  if (!commission.amount || commission.amount <= 0) {
    throw new Error("قيمة العمولة غير صحيحة");
  }

  console.log("بدء إضافة عمولة جديدة:", {
    marketerId: commission.marketerId,
    orderId: commission.orderId,
    orderNumber: commission.orderNumber,
    amount: commission.amount,
    status: commission.status,
    incrementOrderCount
  });

  const commissions = getCommissions();
  const now = new Date().toISOString();
  const newCommission: Commission = {
    id: `c${Date.now()}`,
    createdAt: now,
    updatedAt: now,
    ...commission,
  };

  // إضافة العمولة إلى قائمة العمولات
  commissions.push(newCommission);
  localStorage.setItem(STORAGE_KEYS.COMMISSIONS, JSON.stringify(commissions));
  console.log("تمت إضافة العمولة إلى قائمة العمولات:", newCommission.id);

  // لا نقوم باستدعاء addCommissionToMarketer هنا لأننا نقوم بتحديث بيانات المسوق مباشرة في addOrderCommission

  return newCommission;
};

// تحديث حالة العمولة
export const updateCommissionStatus = (id: string, status: Commission["status"]): Commission => {
  console.log("بدء تحديث حالة العمولة:", { commissionId: id, newStatus: status });

  const commissions = getCommissions();
  const index = commissions.findIndex((commission) => commission.id === id);

  if (index !== -1) {
    const oldStatus = commissions[index].status;
    const marketerId = commissions[index].marketerId;
    const amount = commissions[index].amount;

    console.log("معلومات العمولة قبل التحديث:", {
      commissionId: id,
      marketerId,
      amount,
      oldStatus,
      newStatus: status
    });

    commissions[index] = {
      ...commissions[index],
      status,
      updatedAt: new Date().toISOString(),
    };

    localStorage.setItem(STORAGE_KEYS.COMMISSIONS, JSON.stringify(commissions));
    console.log("تم تحديث حالة العمولة في التخزين المحلي");

    // تحديث إحصائيات المسوق
    updateMarketerStats(marketerId);
    console.log("تم تحديث إحصائيات المسوق بعد تغيير حالة العمولة");

    return commissions[index];
  }

  console.error("العمولة غير موجودة:", id);
  throw new Error("العمولة غير موجودة");
};

// الحصول على جميع عمليات السحب
export const getWithdrawals = (): Withdrawal[] => {
  const storedWithdrawals = localStorage.getItem(STORAGE_KEYS.WITHDRAWALS);
  if (storedWithdrawals) {
    return JSON.parse(storedWithdrawals);
  }
  return [];
};

// الحصول على عمليات سحب مسوق محدد
export const getWithdrawalsByMarketerId = (marketerId: string): Withdrawal[] => {
  const withdrawals = getWithdrawals();
  return withdrawals.filter((withdrawal) => withdrawal.marketerId === marketerId);
};

// إضافة عملية سحب جديدة
export const addWithdrawal = (withdrawal: Omit<Withdrawal, "id" | "createdAt" | "updatedAt">): Withdrawal => {
  const withdrawals = getWithdrawals();
  const now = new Date().toISOString();
  const newWithdrawal: Withdrawal = {
    id: `w${Date.now()}`,
    createdAt: now,
    updatedAt: now,
    ...withdrawal,
  };

  withdrawals.push(newWithdrawal);
  localStorage.setItem(STORAGE_KEYS.WITHDRAWALS, JSON.stringify(withdrawals));

  // تحديث إحصائيات المسوق إذا كانت العملية مكتملة
  if (withdrawal.status === "completed") {
    updateMarketerStats(withdrawal.marketerId);
  }

  return newWithdrawal;
};

// تحديث حالة عملية السحب
export const updateWithdrawalStatus = (id: string, status: Withdrawal["status"]): Withdrawal => {
  const withdrawals = getWithdrawals();
  const index = withdrawals.findIndex((withdrawal) => withdrawal.id === id);

  if (index !== -1) {
    const withdrawal = withdrawals[index];
    const oldStatus = withdrawal.status;

    // تحديث حالة السحب
    withdrawals[index] = {
      ...withdrawal,
      status,
      updatedAt: new Date().toISOString(),
    };

    localStorage.setItem(STORAGE_KEYS.WITHDRAWALS, JSON.stringify(withdrawals));

    // إذا تم تغيير الحالة من "pending" إلى "completed"، قم بتحديث العمولات المعلقة
    if (oldStatus === "pending" && status === "completed") {
      // تحديث العمولات المعلقة للمسوق
      const marketers = getMarketers();
      const marketerIndex = marketers.findIndex((marketer) => marketer.id === withdrawal.marketerId);

      if (marketerIndex !== -1) {
        const marketer = marketers[marketerIndex];

        // تحديث العمولات المعلقة والمسحوبة
        const newPendingCommission = marketer.pendingCommission - withdrawal.amount;
        const newWithdrawnCommission = marketer.withdrawnCommission + withdrawal.amount;

        console.log("تحديث العمولات بعد الموافقة على السحب:", {
          marketerId: marketer.id,
          withdrawalAmount: withdrawal.amount,
          oldPendingCommission: marketer.pendingCommission,
          newPendingCommission,
          oldWithdrawnCommission: marketer.withdrawnCommission,
          newWithdrawnCommission
        });

        // تحديث بيانات المسوق
        marketers[marketerIndex] = {
          ...marketer,
          pendingCommission: newPendingCommission,
          withdrawnCommission: newWithdrawnCommission,
          updatedAt: new Date().toISOString(),
        };

        // حفظ التغييرات
        localStorage.setItem(STORAGE_KEYS.MARKETERS, JSON.stringify(marketers));
      }
    } else {
      // في الحالات الأخرى، قم بتحديث إحصائيات المسوق
      updateMarketerStats(withdrawal.marketerId);
    }

    return withdrawals[index];
  }

  throw new Error("عملية السحب غير موجودة");
};

// إضافة عمولة جديدة إلى حساب المسوق
export const addCommissionToMarketer = (marketerId: string, amount: number, incrementOrderCount: boolean = false): void => {
  if (!marketerId) {
    console.error("معرف المسوق غير محدد");
    return;
  }

  if (!amount || amount <= 0) {
    console.error("قيمة العمولة غير صالحة:", amount);
    return;
  }

  console.log("بدء إضافة عمولة إلى حساب المسوق:", {
    marketerId,
    amount,
    incrementOrderCount
  });

  const marketers = getMarketers();
  const marketerIndex = marketers.findIndex((marketer) => marketer.id === marketerId);

  if (marketerIndex !== -1) {
    // الحصول على البيانات الحالية للمسوق
    const currentMarketer = marketers[marketerIndex];

    // إضافة العمولة الجديدة إلى العمولة الحالية
    const newTotalCommission = currentMarketer.totalCommission + amount;
    const newPendingCommission = currentMarketer.pendingCommission + amount;

    // زيادة عدد الطلبات فقط إذا كان الوسيط incrementOrderCount صحيحًا
    const newOrdersCount = incrementOrderCount ? currentMarketer.ordersCount + 1 : currentMarketer.ordersCount;

    console.log("تحديث بيانات المسوق:", {
      marketerId,
      marketerName: currentMarketer.name,
      oldTotalCommission: currentMarketer.totalCommission,
      newTotalCommission,
      oldPendingCommission: currentMarketer.pendingCommission,
      newPendingCommission,
      oldOrdersCount: currentMarketer.ordersCount,
      newOrdersCount,
      incrementOrderCount
    });

    // تحديث بيانات المسوق
    const updatedMarketer = {
      ...currentMarketer,
      totalCommission: newTotalCommission,
      pendingCommission: newPendingCommission,
      ordersCount: newOrdersCount,
      updatedAt: new Date().toISOString(),
    };

    // تحديث المسوق في المصفوفة
    marketers[marketerIndex] = updatedMarketer;

    // حفظ التغييرات في التخزين المحلي
    localStorage.setItem(STORAGE_KEYS.MARKETERS, JSON.stringify(marketers));

    console.log("تم تحديث بيانات المسوق بنجاح:", {
      marketerId,
      marketerName: updatedMarketer.name,
      totalCommission: updatedMarketer.totalCommission,
      pendingCommission: updatedMarketer.pendingCommission,
      ordersCount: updatedMarketer.ordersCount
    });
  } else {
    console.error("لم يتم العثور على المسوق:", marketerId);
  }
};

// تحديث إحصائيات المسوق
export const updateMarketerStats = (marketerId: string): void => {
  console.log("بدء تحديث إحصائيات المسوق:", marketerId);

  const marketers = getMarketers();
  const marketerIndex = marketers.findIndex((marketer) => marketer.id === marketerId);

  if (marketerIndex !== -1) {
    // الحصول على البيانات الحالية للمسوق
    const currentMarketer = marketers[marketerIndex];
    console.log("بيانات المسوق الحالية:", {
      name: currentMarketer.name,
      totalCommission: currentMarketer.totalCommission,
      pendingCommission: currentMarketer.pendingCommission,
      withdrawnCommission: currentMarketer.withdrawnCommission,
      ordersCount: currentMarketer.ordersCount
    });

    // الحصول على العمولات وعمليات السحب
    const commissions = getCommissionsByMarketerId(marketerId);
    const withdrawals = getWithdrawalsByMarketerId(marketerId);
    console.log("عدد العمولات:", commissions.length);
    console.log("عدد عمليات السحب:", withdrawals.length);

    // حساب إجمالي العمولات (فقط العمولات التي لم يتم إلغاؤها)
    const totalCommission = commissions
      .filter((commission) => commission.status !== "cancelled")
      .reduce((sum, commission) => sum + commission.amount, 0);

    // حساب العمولات المسحوبة
    const withdrawnCommission = withdrawals
      .filter((withdrawal) => withdrawal.status === "completed")
      .reduce((sum, withdrawal) => sum + withdrawal.amount, 0);

    // حساب العمولات المعلقة (إجمالي العمولات - العمولات المسحوبة)
    const pendingCommission = totalCommission - withdrawnCommission;

    // حساب عدد الطلبات
    const ordersCount = commissions
      .filter((commission) => commission.status !== "cancelled")
      .length;

    console.log("الإحصائيات المحسوبة:", {
      totalCommission,
      pendingCommission,
      withdrawnCommission,
      ordersCount
    });

    // تحديث بيانات المسوق
    const updatedMarketer = {
      ...currentMarketer,
      totalCommission,
      pendingCommission,
      withdrawnCommission,
      ordersCount,
      updatedAt: new Date().toISOString(),
    };

    // تحديث المسوق في المصفوفة
    marketers[marketerIndex] = updatedMarketer;

    // حفظ التغييرات في التخزين المحلي
    localStorage.setItem(STORAGE_KEYS.MARKETERS, JSON.stringify(marketers));
    console.log("تم تحديث بيانات المسوق في التخزين المحلي");
  } else {
    console.error("لم يتم العثور على المسوق:", marketerId);
  }
};

// إضافة عمولة عند تسليم طلب
export const addOrderCommission = (
  marketerId: string,
  orderId: string,
  orderNumber: string,
  amount: number,
  isPartialDelivery: boolean = false
): Commission | null => {
  // التحقق من صحة المعلمات
  if (!marketerId || !orderId || !amount || amount <= 0) {
    console.error("معلمات غير صالحة:", { marketerId, orderId, amount });
    return null;
  }

  // التحقق من وجود المسوق
  const marketer = getMarketerById(marketerId);
  if (!marketer) {
    console.error("المسوق غير موجود:", marketerId);
    return null;
  }

  console.log("بدء إضافة عمولة للطلب:", {
    marketerId,
    orderId,
    orderNumber,
    amount,
    isPartialDelivery,
    marketerName: marketer.name
  });

  // التحقق مما إذا كانت هناك عمولة موجودة بالفعل لهذا الطلب
  const existingCommissions = getCommissions();
  const existingCommission = existingCommissions.find(
    (commission) => commission.orderId === orderId && commission.marketerId === marketerId
  );

  // إذا كانت هناك عمولة موجودة بالفعل، قم بإلغائها أولاً
  if (existingCommission) {
    console.log("تم العثور على عمولة موجودة بالفعل:", {
      commissionId: existingCommission.id,
      status: existingCommission.status,
      amount: existingCommission.amount
    });

    try {
      // بدلاً من إلغاء العمولة القديمة، سنقوم بتحديث حالتها مباشرة
      const commissions = getCommissions();
      const index = commissions.findIndex((c) => c.id === existingCommission.id);
      if (index !== -1) {
        commissions[index] = {
          ...commissions[index],
          status: "cancelled",
          updatedAt: new Date().toISOString(),
        };

        localStorage.setItem(STORAGE_KEYS.COMMISSIONS, JSON.stringify(commissions));
        console.log("تم تحديث حالة العمولة القديمة إلى 'ملغية' في التخزين المحلي");
      }
    } catch (error) {
      console.error("خطأ أثناء إلغاء العمولة القديمة:", error);
    }
  }

  // في حالة التسليم الجزئي، لا نزيد عدد الطلبات لأنه نفس الطلب
  // في حالة التسليم الكامل، نزيد عدد الطلبات
  const incrementOrderCount = !isPartialDelivery;
  console.log("زيادة عدد الطلبات:", incrementOrderCount);

  try {
    // إضافة العمولة مباشرة إلى حساب المسوق بدلاً من استدعاء updateMarketerStats
    // هذا سيضمن أن العمولة تضاف بشكل صحيح
    const marketers = getMarketers();
    const marketerIndex = marketers.findIndex((m) => m.id === marketerId);

    if (marketerIndex !== -1) {
      const currentMarketer = marketers[marketerIndex];

      // إضافة العمولة الجديدة إلى العمولة الحالية
      const newTotalCommission = currentMarketer.totalCommission + amount;
      const newPendingCommission = currentMarketer.pendingCommission + amount;

      // زيادة عدد الطلبات فقط إذا كان التسليم كامل
      const newOrdersCount = incrementOrderCount ? currentMarketer.ordersCount + 1 : currentMarketer.ordersCount;

      console.log("تحديث بيانات المسوق مباشرة:", {
        marketerId,
        marketerName: currentMarketer.name,
        oldTotalCommission: currentMarketer.totalCommission,
        newTotalCommission,
        oldPendingCommission: currentMarketer.pendingCommission,
        newPendingCommission,
        oldOrdersCount: currentMarketer.ordersCount,
        newOrdersCount
      });

      // تحديث بيانات المسوق
      marketers[marketerIndex] = {
        ...currentMarketer,
        totalCommission: newTotalCommission,
        pendingCommission: newPendingCommission,
        ordersCount: newOrdersCount,
        updatedAt: new Date().toISOString(),
      };

      // حفظ التغييرات في التخزين المحلي
      localStorage.setItem(STORAGE_KEYS.MARKETERS, JSON.stringify(marketers));
    }

    // إضافة العمولة إلى سجل العمولات مباشرة بدلاً من استدعاء addCommission
    const commissions = getCommissions();
    const now = new Date().toISOString();
    const newCommission: Commission = {
      id: `c${Date.now()}`,
      marketerId,
      orderId,
      orderNumber,
      amount,
      status: "pending",
      createdAt: now,
      updatedAt: now,
    };

    // إضافة العمولة إلى قائمة العمولات
    commissions.push(newCommission);
    localStorage.setItem(STORAGE_KEYS.COMMISSIONS, JSON.stringify(commissions));
    console.log("تمت إضافة العمولة إلى قائمة العمولات مباشرة:", newCommission.id);

    console.log("تمت إضافة العمولة بنجاح:", {
      commissionId: newCommission.id,
      amount: newCommission.amount,
      status: newCommission.status
    });

    // تحديث بيانات المسوق مباشرة
    const updatedMarketer = getMarketerById(marketerId);
    console.log("بيانات المسوق بعد التحديث:", {
      totalCommission: updatedMarketer?.totalCommission,
      pendingCommission: updatedMarketer?.pendingCommission,
      ordersCount: updatedMarketer?.ordersCount
    });

    return newCommission;
  } catch (error) {
    console.error("خطأ أثناء إضافة العمولة:", error);
    return null;
  }
};

// تحديث حالة العمولة عند تسليم الطلب
export const approveOrderCommission = (orderId: string): Commission | null => {
  const commissions = getCommissions();
  const commission = commissions.find((c) => c.orderId === orderId && c.status === "pending");

  if (commission) {
    return updateCommissionStatus(commission.id, "approved");
  }

  return null;
};

// إلغاء العمولة عند رفض الطلب
export const cancelOrderCommission = (orderId: string): Commission | null => {
  console.log("بدء إلغاء العمولة للطلب:", orderId);

  const commissions = getCommissions();
  const commission = commissions.find((c) => c.orderId === orderId && (c.status === "pending" || c.status === "approved"));

  if (commission) {
    console.log("تم العثور على العمولة المراد إلغاؤها:", {
      commissionId: commission.id,
      marketerId: commission.marketerId,
      amount: commission.amount,
      status: commission.status
    });

    try {
      // تحديث حالة العمولة مباشرة بدلاً من استدعاء updateCommissionStatus
      const index = commissions.findIndex((c) => c.id === commission.id);
      if (index !== -1) {
        commissions[index] = {
          ...commissions[index],
          status: "cancelled",
          updatedAt: new Date().toISOString(),
        };

        localStorage.setItem(STORAGE_KEYS.COMMISSIONS, JSON.stringify(commissions));
        console.log("تم تحديث حالة العمولة إلى 'ملغية' في التخزين المحلي");

        return commissions[index];
      }

      return null;
    } catch (error) {
      console.error("خطأ أثناء إلغاء العمولة:", error);
      return null;
    }
  } else {
    console.log("لم يتم العثور على عمولة للطلب:", orderId);
    return null;
  }
};
