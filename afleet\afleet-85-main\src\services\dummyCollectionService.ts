// خدمات التحصيل والمتابعة (بيانات تجريبية)

// الحصول على شركة شحن بواسطة المعرف
export const getShippingCompanyById = (id: string) => {
  return {
    id,
    name: "شركة الشحن التجريبية",
    phone: "01234567890",
    email: "<EMAIL>",
    address: "عنوان تجريبي",
    contactPerson: "اسم تجريبي",
    balance: 5000,
    createdAt: new Date().toISOString(),
    zones: [
      {
        id: "zone1",
        name: "المنطقة 1",
        price: "50",
        createdAt: new Date().toISOString()
      },
      {
        id: "zone2",
        name: "المنطقة 2",
        price: "70",
        createdAt: new Date().toISOString()
      }
    ]
  };
};

// الحصول على المدفوعات لشركة شحن
export const getPaymentsByCompanyId = (companyId: string) => {
  return [
    {
      id: "payment1",
      companyId,
      amount: 1000,
      date: new Date().toISOString(),
      notes: "دفعة تجريبية 1",
      createdAt: new Date().toISOString()
    },
    {
      id: "payment2",
      companyId,
      amount: 2000,
      date: new Date(Date.now() - 86400000).toISOString(), // بالأمس
      notes: "دفعة تجريبية 2",
      createdAt: new Date(Date.now() - 86400000).toISOString()
    }
  ];
};

// الحصول على المنتجات المرتجعة لشركة شحن
export const getReturnedProductsByCompanyId = (companyId: string) => {
  return [
    {
      id: "return1",
      companyId,
      orderNumber: "12345",
      productName: "منتج تجريبي 1",
      quantity: 2,
      returnReason: "تالف",
      date: new Date().toISOString(),
      createdAt: new Date().toISOString()
    },
    {
      id: "return2",
      companyId,
      orderNumber: "67890",
      productName: "منتج تجريبي 2",
      quantity: 1,
      returnReason: "غير مطابق للمواصفات",
      date: new Date(Date.now() - 86400000).toISOString(), // بالأمس
      createdAt: new Date(Date.now() - 86400000).toISOString()
    }
  ];
};

// إضافة دفعة جديدة
export const addPayment = (payment: any) => {
  return {
    id: `payment-${Date.now()}`,
    createdAt: new Date().toISOString(),
    ...payment
  };
};

// تحديث شركة شحن
export const updateShippingCompany = (id: string, data: any) => {
  return {
    id,
    ...data,
    updatedAt: new Date().toISOString()
  };
};
