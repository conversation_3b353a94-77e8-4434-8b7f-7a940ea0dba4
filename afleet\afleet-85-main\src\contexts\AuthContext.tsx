
import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { toast } from "sonner";
import { Employee } from "@/types/employeeTypes";
import { sanitizeInput, validateEmail, rateLimiter } from "@/utils/validation";

// البيانات المباشرة للموظفين (مضمنة في الكود)
const DIRECT_EMPLOYEES: Employee[] = [
  {
    "id": "emp-1",
    "name": "أحمد محمد",
    "email": "admin",
    "password": "admin123",
    "phone": "01012345678",
    "role": "admin",
    "accessibleSections": [
      "dashboard", "products", "categories", "orders", "warehouse",
      "shipping", "delivery", "archive", "marketers", "commissions",
      "shipping-settings", "reports", "settings", "site-settings"
    ],
    "isActive": true,
    "createdAt": "2025-06-01T19:21:31.116Z"
  },
  {
    "id": "emp-2",
    "name": "محمد علي",
    "email": "<EMAIL>",
    "password": "sales123",
    "phone": "01112345678",
    "role": "sales",
    "accessibleSections": ["dashboard", "products", "categories", "orders"],
    "isActive": true,
    "createdAt": "2025-06-01T19:21:31.117Z"
  },
  {
    "id": "emp-3",
    "name": "سارة أحمد",
    "email": "<EMAIL>",
    "password": "warehouse123",
    "phone": "01212345678",
    "role": "warehouse",
    "accessibleSections": ["dashboard", "warehouse", "products"],
    "isActive": true,
    "createdAt": "2025-06-01T19:21:31.117Z"
  }
];

// دوال مساعدة مباشرة
const findDirectEmployee = (identifier: string): Employee | null => {
  const cleanId = identifier.toLowerCase().trim();
  console.log('🔍 البحث المباشر عن:', cleanId);

  // البحث بالإيميل أولاً
  let employee = DIRECT_EMPLOYEES.find(emp => emp.email.toLowerCase() === cleanId);

  // إذا لم يوجد، ابحث بالاسم
  if (!employee) {
    employee = DIRECT_EMPLOYEES.find(emp => emp.name.toLowerCase() === cleanId);
  }

  console.log('🔍 نتيجة البحث المباشر:', employee ? `وجد - ${employee.name}` : 'لم يوجد');
  return employee || null;
};

const directLogin = (identifier: string, password: string): Employee | null => {
  console.log('🔐 تسجيل دخول مباشر:', { identifier, password });

  const employee = findDirectEmployee(identifier);

  if (!employee) {
    console.error('❌ لم يتم العثور على موظف');
    return null;
  }

  if (employee.password !== password) {
    console.error('❌ كلمة المرور غير صحيحة');
    console.log('🔍 المحفوظة:', employee.password, '| المدخلة:', password);
    return null;
  }

  if (!employee.isActive) {
    console.error('❌ الحساب غير نشط');
    return null;
  }

  console.log('✅ تسجيل دخول مباشر ناجح:', employee.name);
  return employee;
};

interface AuthContextType {
  user: Employee | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  hasPermission: (section: string, action?: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<Employee | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    console.log('🔄 تهيئة نظام المصادقة...');
    console.log('👥 الموظفين المتاحين:');
    DIRECT_EMPLOYEES.forEach(emp => {
      console.log(`   👤 ${emp.name} | 📧 ${emp.email} | 🔑 ${emp.password} | 🎭 ${emp.role}`);
    });

    // محاولة استعادة الجلسة
    try {
      const savedUser = localStorage.getItem('current_employee');
      if (savedUser) {
        const currentEmployee = JSON.parse(savedUser);
        setUser(currentEmployee);
        console.log('✅ تم استعادة جلسة:', currentEmployee.name);
      }
    } catch (error) {
      console.warn("⚠️ خطأ في تحميل بيانات الموظف:", error);
    }

    // إعادة تعيين Rate Limiter للتطوير
    rateLimiter.reset('user-login');
    setLoading(false);

    console.log('✅ تم تهيئة نظام المصادقة');
  }, []);

  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      // تنظيف وتحقق من المدخلات
      const cleanIdentifier = sanitizeInput(email.toLowerCase().trim()); // يمكن أن يكون اسم أو إيميل
      const cleanPassword = sanitizeInput(password);

      console.log('🔍 محاولة تسجيل دخول:', {
        identifier: cleanIdentifier,
        identifierLength: cleanIdentifier.length,
        passwordLength: cleanPassword.length,
        originalEmail: email,
        originalPassword: password
      });

      // التحقق من صحة المدخل (إيميل أو اسم)
      const isEmail = cleanIdentifier.includes('@');
      if (isEmail && !validateEmail(cleanIdentifier)) {
        throw new Error("البريد الإلكتروني غير صحيح");
      }

      // التحقق من Rate Limiting - 10 محاولات يومياً (زيادة العدد للتطوير)
      const clientIP = 'user-login'; // في الإنتاج، استخدم IP الحقيقي
      const dailyWindow = 24 * 60 * 60 * 1000; // 24 ساعة بالميلي ثانية
      if (!rateLimiter.isAllowed(clientIP, 10, dailyWindow)) { // 10 محاولات كل 24 ساعة
        console.warn('⚠️ تم تجاوز حد المحاولات');
        throw new Error("تم تجاوز عدد المحاولات المسموح (10 محاولات يومياً). حاول مرة أخرى غداً");
      }

      // محاكاة تأخير API
      await new Promise((resolve) => setTimeout(resolve, 500));

      console.log('🔍 البحث عن الموظف...');
      console.log('🔍 البيانات المرسلة:', { identifier: cleanIdentifier, password: cleanPassword });

      // البحث المباشر في البيانات
      console.log('🔍 البحث في قائمة الموظفين...');
      let employee = null;

      for (const emp of DIRECT_EMPLOYEES) {
        console.log(`🔍 فحص: ${emp.name} (${emp.email})`);

        // مقارنة الإيميل أو الاسم
        if (emp.email.toLowerCase() === cleanIdentifier || emp.name.toLowerCase() === cleanIdentifier) {
          console.log('✅ تم العثور على الموظف');

          // فحص كلمة المرور
          if (emp.password === cleanPassword) {
            console.log('✅ كلمة المرور صحيحة');

            if (emp.isActive) {
              console.log('✅ الحساب نشط');
              employee = emp;
              break;
            } else {
              console.error('❌ الحساب غير نشط');
              throw new Error("الحساب غير نشط");
            }
          } else {
            console.error('❌ كلمة المرور غير صحيحة');
            console.log('🔍 المحفوظة:', emp.password, '| المدخلة:', cleanPassword);
            throw new Error("كلمة المرور غير صحيحة");
          }
        }
      }

      console.log('🔍 نتيجة البحث:', employee ? `وجد - ${employee.name}` : 'لم يوجد');

      if (employee) {
        console.log('✅ تسجيل دخول ناجح:', employee.name, '| الدور:', employee.role);

        // حفظ الموظف الحالي
        try {
          localStorage.setItem('current_employee', JSON.stringify(employee));
          console.log('💾 تم حفظ الجلسة');
        } catch (error) {
          console.warn('⚠️ فشل في حفظ الجلسة:', error);
        }

        setUser(employee);
        toast.success(`مرحباً ${employee.name}! تم تسجيل الدخول بنجاح`);
      } else {
        console.error('❌ لم يتم العثور على موظف بهذه البيانات');
        console.log('🔍 جرب البيانات التالية:');
        console.log('   - admin / admin123');
        console.log('   - <EMAIL> / sales123');
        console.log('   - <EMAIL> / warehouse123');
        throw new Error("لم يتم العثور على موظف بهذه البيانات");
      }
    } catch (error) {
      console.error('❌ خطأ في تسجيل الدخول:', error);
      if (error instanceof Error) {
        toast.error(error.message || "خطأ في تسجيل الدخول. يرجى التحقق من البريد الإلكتروني وكلمة المرور");
      } else {
        toast.error("خطأ في تسجيل الدخول. يرجى التحقق من البريد الإلكتروني وكلمة المرور");
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setUser(null);

    // مسح الجلسة
    try {
      localStorage.removeItem('current_employee');
      sessionStorage.removeItem('current_employee');
    } catch (error) {
      console.warn('⚠️ خطأ في مسح الجلسة:', error);
    }

    // مسح أي بيانات مخزنة لاسم المستخدم وكلمة المرور
    sessionStorage.clear();

    // مسح كوكيز المتصفح المتعلقة بالنموذج
    document.cookie.split(";").forEach(c => {
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
    });

    // مسح بيانات النموذج من ذاكرة المتصفح
    try {
      window.history.replaceState({}, document.title, window.location.pathname);
    } catch (e) {
      console.error("خطأ في مسح بيانات النموذج:", e);
    }

    toast.success("تم تسجيل الخروج بنجاح");
  };

  const checkPermission = (section: string, action: string = "view") => {
    if (!user) return false;

    // المدير لديه جميع الصلاحيات
    if (user.role === "admin") return true;

    // التحقق من الأقسام المتاحة
    if (user.accessibleSections) {
      return user.accessibleSections.includes(section);
    }

    return false;
  };

  return (
    <AuthContext.Provider value={{ user, loading, login, logout, hasPermission: checkPermission }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
