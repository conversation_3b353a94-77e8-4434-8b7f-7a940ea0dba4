
import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { toast } from "sonner";
import { Employee } from "@/types/employeeTypes";

// الأدمن الافتراضي (مضمن في الكود)
const DEFAULT_ADMIN: Employee = {
  "id": "emp-1",
  "name": "أحمد محمد",
  "email": "admin",
  "password": "admin123",
  "phone": "01012345678",
  "role": "admin",
  "accessibleSections": [
    "dashboard", "products", "categories", "orders", "warehouse",
    "shipping", "delivery", "archive", "marketers", "commissions",
    "shipping-settings", "reports", "settings", "site-settings"
  ],
  "isActive": true,
  "createdAt": "2025-06-01T19:21:31.116Z"
};

// متغير لحفظ الموظف الحالي
let currentUser: Employee | null = null;

interface AuthContextType {
  user: Employee | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  hasPermission: (section: string, action?: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // تسجيل دخول تلقائي كأدمن
  const [user, setUser] = useState<Employee | null>(DEFAULT_ADMIN);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    console.log('🚀 تسجيل دخول تلقائي كأدمن...');
    console.log('👤 مسجل دخول كـ:', DEFAULT_ADMIN.name);
    console.log('🎭 الدور:', DEFAULT_ADMIN.role);
    console.log('✅ تم تجاوز نظام تسجيل الدخول للاستضافة');

    // حفظ في المتغير العام
    currentUser = DEFAULT_ADMIN;

    // حفظ في localStorage للاستمرارية
    try {
      localStorage.setItem('current_employee', JSON.stringify(DEFAULT_ADMIN));
    } catch (error) {
      console.warn('⚠️ فشل في حفظ الجلسة:', error);
    }
  }, []);

  const login = async (email: string, password: string) => {
    console.log('🚀 تسجيل دخول تلقائي (تجاهل البيانات المدخلة)');

    // تسجيل دخول تلقائي كأدمن بغض النظر عن البيانات المدخلة
    setUser(DEFAULT_ADMIN);
    currentUser = DEFAULT_ADMIN;

    toast.success(`مرحباً ${DEFAULT_ADMIN.name}! تم تسجيل الدخول بنجاح`);
    console.log('✅ تم تسجيل الدخول كأدمن تلقائ<|im_start|>');
  };

  const logout = () => {
    console.log('🚪 تسجيل خروج...');

    // إعادة تسجيل دخول تلقائي كأدمن
    setUser(DEFAULT_ADMIN);
    currentUser = DEFAULT_ADMIN;

    toast.success("تم تسجيل الخروج وإعادة الدخول كأدمن");
    console.log('✅ تم إعادة تسجيل الدخول كأدمن تلقائ<|im_start|>');
  };

  const checkPermission = (section: string, action: string = "view") => {
    if (!user) return false;

    // المدير لديه جميع الصلاحيات
    if (user.role === "admin") return true;

    // التحقق من الأقسام المتاحة
    if (user.accessibleSections) {
      return user.accessibleSections.includes(section);
    }

    return false;
  };

  return (
    <AuthContext.Provider value={{ user, loading, login, logout, hasPermission: checkPermission }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
