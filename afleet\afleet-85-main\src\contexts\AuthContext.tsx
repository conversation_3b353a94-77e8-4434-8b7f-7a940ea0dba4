
import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { toast } from "sonner";
import { Employee } from "@/types/employeeTypes";
import { sanitizeInput, validateEmail, rateLimiter } from "@/utils/validation";
import {
  getCurrentUnifiedEmployee,
  unifiedLogin,
  unifiedLogout,
  hasUnifiedPermission,
  initializeUnifiedEmployees
} from "@/services/unifiedEmployeeService";
import {
  initializeForProduction,
  emergencyLoginRestore,
  checkLocalStorageHealth
} from "@/services/storageFixService";

interface AuthContextType {
  user: Employee | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  hasPermission: (section: string, action?: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<Employee | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    // فحص حالة localStorage أولاً
    const health = checkLocalStorageHealth();
    if (!health.isWorking) {
      console.warn('⚠️ مشاكل في localStorage، تطبيق الإصلاحات...');
      emergencyLoginRestore();
    }

    // تهيئة النظام للاستضافة
    initializeForProduction();

    // تهيئة النظام الموحد للموظفين
    initializeUnifiedEmployees();

    // إعادة تعيين Rate Limiter للتطوير
    rateLimiter.reset('user-login');
    console.log('🔄 تم إعادة تعيين Rate Limiter');

    // التحقق من وجود موظف مسجل الدخول
    try {
      const currentEmployee = getCurrentUnifiedEmployee();
      if (currentEmployee) {
        setUser(currentEmployee);
      }
    } catch (error) {
      console.error("خطأ في تحميل بيانات الموظف:", error);
    } finally {
      setLoading(false);
    }
  }, []);

  const login = async (email: string, password: string) => {
    setLoading(true);
    try {
      // تنظيف وتحقق من المدخلات
      const cleanIdentifier = sanitizeInput(email.toLowerCase().trim()); // يمكن أن يكون اسم أو إيميل
      const cleanPassword = sanitizeInput(password);

      console.log('🔍 محاولة تسجيل دخول:', {
        identifier: cleanIdentifier,
        identifierLength: cleanIdentifier.length,
        passwordLength: cleanPassword.length,
        originalEmail: email,
        originalPassword: password
      });

      // التحقق من صحة المدخل (إيميل أو اسم)
      const isEmail = cleanIdentifier.includes('@');
      if (isEmail && !validateEmail(cleanIdentifier)) {
        throw new Error("البريد الإلكتروني غير صحيح");
      }

      // التحقق من Rate Limiting - 10 محاولات يومياً (زيادة العدد للتطوير)
      const clientIP = 'user-login'; // في الإنتاج، استخدم IP الحقيقي
      const dailyWindow = 24 * 60 * 60 * 1000; // 24 ساعة بالميلي ثانية
      if (!rateLimiter.isAllowed(clientIP, 10, dailyWindow)) { // 10 محاولات كل 24 ساعة
        console.warn('⚠️ تم تجاوز حد المحاولات');
        throw new Error("تم تجاوز عدد المحاولات المسموح (10 محاولات يومياً). حاول مرة أخرى غداً");
      }

      // محاكاة تأخير API
      await new Promise((resolve) => setTimeout(resolve, 500));

      console.log('🔍 البحث عن الموظف...');
      console.log('🔍 البيانات المرسلة:', { identifier: cleanIdentifier, password: cleanPassword });

      // تجربة تسجيل الدخول بالنظام الموحد
      const employee = unifiedLogin(cleanIdentifier, cleanPassword);
      console.log('🔍 نتيجة تسجيل الدخول:', employee);

      if (employee) {
        console.log('✅ تم العثور على الموظف:', employee.name);
        setUser(employee);
        toast.success(`مرحباً ${employee.name}! تم تسجيل الدخول بنجاح`);
      } else {
        console.error('❌ بيانات غير صحيحة');
        console.log('🔍 جرب البيانات التالية:');
        console.log('   - admin / admin123');
        console.log('   - <EMAIL> / sales123');
        throw new Error("الاسم أو البريد الإلكتروني أو كلمة المرور غير صحيحة");
      }
    } catch (error) {
      console.error('❌ خطأ في تسجيل الدخول:', error);
      if (error instanceof Error) {
        toast.error(error.message || "خطأ في تسجيل الدخول. يرجى التحقق من البريد الإلكتروني وكلمة المرور");
      } else {
        toast.error("خطأ في تسجيل الدخول. يرجى التحقق من البريد الإلكتروني وكلمة المرور");
      }
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    unifiedLogout();

    // مسح أي بيانات مخزنة لاسم المستخدم وكلمة المرور
    sessionStorage.clear();

    // مسح كوكيز المتصفح المتعلقة بالنموذج
    document.cookie.split(";").forEach(c => {
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
    });

    // مسح بيانات النموذج من ذاكرة المتصفح
    try {
      window.history.replaceState({}, document.title, window.location.pathname);
    } catch (e) {
      console.error("خطأ في مسح بيانات النموذج:", e);
    }

    toast.success("تم تسجيل الخروج بنجاح");
  };

  const checkPermission = (section: string, action: string = "view") => {
    return hasUnifiedPermission(user, section, action);
  };

  return (
    <AuthContext.Provider value={{ user, loading, login, logout, hasPermission: checkPermission }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
