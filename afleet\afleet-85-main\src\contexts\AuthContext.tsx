
import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { toast } from "sonner";
import { Employee } from "@/types/employeeTypes";

// بيانات الموظفين مع الصلاحيات
const EMPLOYEES_DATA: Employee[] = [
  {
    "id": "emp-1",
    "name": "أحمد محمد",
    "email": "admin",
    "password": "admin123",
    "phone": "01012345678",
    "role": "admin",
    "accessibleSections": [
      "dashboard", "products", "categories", "orders", "warehouse",
      "shipping", "delivery", "archive", "marketers", "commissions",
      "shipping-settings", "reports", "settings", "site-settings"
    ],
    "permissions": [
      {
        "section": "dashboard",
        "actions": ["view", "create", "edit", "delete", "approve", "reject", "export", "import"]
      },
      {
        "section": "orders",
        "actions": ["view", "create", "edit", "delete", "approve", "reject", "export", "import"]
      },
      {
        "section": "products",
        "actions": ["view", "create", "edit", "delete", "approve", "reject", "export", "import"]
      },
      {
        "section": "settings",
        "actions": ["view", "create", "edit", "delete", "approve", "reject", "export", "import"]
      }
    ],
    "isActive": true,
    "createdAt": "2025-06-01T19:21:31.116Z"
  },
  {
    "id": "emp-2",
    "name": "محمد علي",
    "email": "<EMAIL>",
    "password": "sales123",
    "phone": "01112345678",
    "role": "sales",
    "accessibleSections": ["dashboard", "products", "categories", "orders"],
    "permissions": [
      {
        "section": "dashboard",
        "actions": ["view"]
      },
      {
        "section": "orders",
        "actions": ["view", "create", "edit", "export"]
      },
      {
        "section": "products",
        "actions": ["view"]
      },
      {
        "section": "categories",
        "actions": ["view"]
      }
    ],
    "isActive": true,
    "createdAt": "2025-06-01T19:21:31.117Z"
  },
  {
    "id": "emp-3",
    "name": "سارة أحمد",
    "email": "<EMAIL>",
    "password": "warehouse123",
    "phone": "01212345678",
    "role": "warehouse",
    "accessibleSections": ["dashboard", "warehouse", "products"],
    "permissions": [
      {
        "section": "dashboard",
        "actions": ["view"]
      },
      {
        "section": "warehouse",
        "actions": ["view", "edit", "approve", "reject"]
      },
      {
        "section": "products",
        "actions": ["view", "edit"]
      }
    ],
    "isActive": true,
    "createdAt": "2025-06-01T19:21:31.117Z"
  }
];

// دوال مساعدة للبحث والتحقق
const findEmployee = (identifier: string): Employee | null => {
  const cleanId = identifier.toLowerCase().trim();

  // البحث بالإيميل أولاً
  let employee = EMPLOYEES_DATA.find(emp => emp.email.toLowerCase() === cleanId);

  // إذا لم يوجد، ابحث بالاسم
  if (!employee) {
    employee = EMPLOYEES_DATA.find(emp => emp.name.toLowerCase() === cleanId);
  }

  return employee || null;
};

const validateLogin = (identifier: string, password: string): Employee | null => {
  const employee = findEmployee(identifier);

  if (!employee) {
    return null;
  }

  if (employee.password !== password) {
    return null;
  }

  if (!employee.isActive) {
    return null;
  }

  return employee;
};

interface AuthContextType {
  user: Employee | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  hasPermission: (section: string, action?: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<Employee | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    console.log('🔄 تهيئة نظام المصادقة...');
    console.log('👥 الموظفين المتاحين:');
    EMPLOYEES_DATA.forEach(emp => {
      console.log(`   👤 ${emp.name} | 📧 ${emp.email} | 🔑 ${emp.password} | 🎭 ${emp.role}`);
      console.log(`      📋 الأقسام: ${emp.accessibleSections?.join(', ')}`);
    });

    // محاولة استعادة الجلسة
    try {
      const savedUser = localStorage.getItem('current_employee');
      if (savedUser) {
        const currentEmployee = JSON.parse(savedUser);
        // التحقق من أن الموظف لا يزال موجود ونشط
        const validEmployee = EMPLOYEES_DATA.find(emp =>
          emp.id === currentEmployee.id && emp.isActive
        );
        if (validEmployee) {
          setUser(validEmployee);
          console.log('✅ تم استعادة جلسة:', validEmployee.name);
        } else {
          localStorage.removeItem('current_employee');
          console.log('⚠️ جلسة غير صالحة، تم مسحها');
        }
      }
    } catch (error) {
      console.warn("⚠️ خطأ في تحميل بيانات الموظف:", error);
      localStorage.removeItem('current_employee');
    }

    setLoading(false);
    console.log('✅ تم تهيئة نظام المصادقة');

    // إضافة دوال اختبار للنافذة
    (window as any).testLogin = (identifier: string, password: string) => {
      console.log('🧪 اختبار تسجيل دخول...');
      const result = validateLogin(identifier, password);
      console.log('🧪 النتيجة:', result ? `✅ نجح - ${result.name} (${result.role})` : '❌ فشل');
      return result;
    };

    (window as any).showEmployees = () => {
      console.log('👥 جميع الموظفين:');
      console.table(EMPLOYEES_DATA.map(emp => ({
        name: emp.name,
        email: emp.email,
        password: emp.password,
        role: emp.role,
        sections: emp.accessibleSections?.join(', '),
        isActive: emp.isActive
      })));
      return EMPLOYEES_DATA;
    };

    (window as any).forceLogin = (role: string = 'admin') => {
      const emp = EMPLOYEES_DATA.find(e => e.role === role) || EMPLOYEES_DATA[0];
      setUser(emp);
      localStorage.setItem('current_employee', JSON.stringify(emp));
      console.log(`🔧 تم فرض تسجيل دخول: ${emp.name} (${emp.role})`);
      return emp;
    };

    console.log('🛠️ دوال الاختبار متاحة:');
    console.log('- testLogin("admin", "admin123")');
    console.log('- showEmployees()');
    console.log('- forceLogin("admin") أو forceLogin("sales")');
  }, []);

  const login = async (email: string, password: string) => {
    console.log('🔐 بدء عملية تسجيل الدخول...');
    console.log('📝 البيانات المدخلة:', { email, password });

    setLoading(true);

    try {
      // تنظيف المدخلات
      const cleanIdentifier = email.toLowerCase().trim();
      const cleanPassword = password.trim();

      console.log('🧹 البيانات بعد التنظيف:', { cleanIdentifier, cleanPassword });

      // محاكاة تأخير API
      await new Promise(resolve => setTimeout(resolve, 500));

      // التحقق من صحة البيانات
      const employee = validateLogin(cleanIdentifier, cleanPassword);

      if (employee) {
        console.log('✅ تسجيل دخول ناجح!');
        console.log('👤 الموظف:', employee.name);
        console.log('🎭 الدور:', employee.role);
        console.log('📋 الأقسام المتاحة:', employee.accessibleSections);

        // حفظ الجلسة
        try {
          localStorage.setItem('current_employee', JSON.stringify(employee));
          console.log('💾 تم حفظ الجلسة');
        } catch (error) {
          console.warn('⚠️ فشل في حفظ الجلسة:', error);
        }

        setUser(employee);
        toast.success(`مرحباً ${employee.name}! تم تسجيل الدخول بنجاح`);

      } else {
        console.error('❌ بيانات تسجيل الدخول غير صحيحة');
        console.log('🔍 تحقق من البيانات التالية:');
        EMPLOYEES_DATA.forEach(emp => {
          console.log(`   👤 ${emp.name}: ${emp.email} / ${emp.password}`);
        });
        throw new Error("البريد الإلكتروني أو كلمة المرور غير صحيحة");
      }

    } catch (error) {
      console.error('❌ خطأ في تسجيل الدخول:', error);
      toast.error(error instanceof Error ? error.message : "خطأ في تسجيل الدخول");
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    console.log('🚪 تسجيل خروج...');

    setUser(null);

    // مسح الجلسة
    try {
      localStorage.removeItem('current_employee');
      sessionStorage.removeItem('current_employee');
      console.log('🗑️ تم مسح الجلسة');
    } catch (error) {
      console.warn('⚠️ خطأ في مسح الجلسة:', error);
    }

    toast.success("تم تسجيل الخروج بنجاح");
    console.log('✅ تم تسجيل الخروج');
  };

  const checkPermission = (section: string, action: string = "view") => {
    if (!user) {
      console.log('❌ لا يوجد مستخدم مسجل دخول');
      return false;
    }

    console.log(`🔍 فحص صلاحية: ${user.name} | القسم: ${section} | الإجراء: ${action}`);

    // المدير لديه جميع الصلاحيات
    if (user.role === "admin") {
      console.log('✅ مدير - صلاحية كاملة');
      return true;
    }

    // التحقق من الصلاحيات المحددة
    if (user.permissions) {
      const sectionPermission = user.permissions.find(p => p.section === section);
      if (sectionPermission && sectionPermission.actions.includes(action)) {
        console.log('✅ صلاحية محددة موجودة');
        return true;
      }
    }

    // التحقق من الأقسام المتاحة (للعرض فقط)
    if (action === "view" && user.accessibleSections) {
      const hasAccess = user.accessibleSections.includes(section);
      console.log(`${hasAccess ? '✅' : '❌'} صلاحية عرض: ${hasAccess}`);
      return hasAccess;
    }

    console.log('❌ لا توجد صلاحية');
    return false;
  };

  return (
    <AuthContext.Provider value={{ user, loading, login, logout, hasPermission: checkPermission }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
