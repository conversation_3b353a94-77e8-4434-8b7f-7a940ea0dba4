import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowRight, Printer, Edit, Check, X, Clock, AlertTriangle, ZoomIn, Trash2, Archive, XCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import ImageModal from "@/components/ui/image-modal";
import { Order, OrderStatus, OrderSection } from "./Orders";
import { getOrderById, updateOrderStatus, updateOrder, invalidateAllOrderQueries } from "@/services/localStorageService";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import OrderSummary from "@/components/orders/OrderSummary";
import OrderActions from "@/components/orders/OrderActions";
import PrintableInvoice from "@/components/orders/PrintableInvoice";
import { printInvoice, InvoiceData, CompanySettings } from "@/utils/invoiceTemplate";

// ترجمة حالة الطلب إلى العربية
const translateStatus = (status: OrderStatus): string => {
  const statusMap: Record<OrderStatus, string> = {
    pending: "قيد الانتظار",
    confirmed: "تم التأكيد",
    processing: "قيد التجهيز",
    shipped: "تم الشحن",
    delivered: "تم التسليم",
    cancelled: "ملغي",
    suspended: "معلق"
  };
  return statusMap[status] || status;
};

// ترجمة طريقة الدفع إلى العربية
const translatePaymentMethod = (method: string): string => {
  const methodMap: Record<string, string> = {
    cash: "الدفع عند الاستلام",
    card: "بطاقة ائتمان",
    bank_transfer: "تحويل بنكي"
  };
  return methodMap[method] || method;
};

// ترجمة حالة الدفع إلى العربية
const translatePaymentStatus = (status: string): string => {
  const statusMap: Record<string, string> = {
    paid: "مدفوع",
    unpaid: "غير مدفوع",
    partially_paid: "مدفوع جزئياً"
  };
  return statusMap[status] || status;
};

// لون حالة الطلب
const getStatusColor = (status: OrderStatus): string => {
  const colorMap: Record<OrderStatus, string> = {
    pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
    confirmed: "bg-blue-100 text-blue-800 border-blue-200",
    processing: "bg-purple-100 text-purple-800 border-purple-200",
    shipped: "bg-indigo-100 text-indigo-800 border-indigo-200",
    delivered: "bg-green-100 text-green-800 border-green-200",
    cancelled: "bg-red-100 text-red-800 border-red-200",
    suspended: "bg-gray-100 text-gray-800 border-gray-200"
  };
  return colorMap[status] || "";
};

const OrderDetailsPage = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);

  // حالة نافذة تأكيد الحذف
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deleteReason, setDeleteReason] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    const fetchOrder = async () => {
      try {
        setLoading(true);
        if (!id) {
          setError("معرف الطلب غير صالح");
          return;
        }

        // محاكاة تأخير API
        await new Promise(resolve => setTimeout(resolve, 500));

        const orderData = getOrderById(id);
        if (!orderData) {
          setError("الطلب غير موجود");
          return;
        }

        setOrder(orderData);
      } catch (err) {
        setError("حدث خطأ أثناء جلب بيانات الطلب");
      } finally {
        setLoading(false);
      }
    };

    fetchOrder();
  }, [id]);

  // Company information for invoice - load from localStorage or use defaults
  const [companySettings, setCompanySettings] = useState({
    companyName: "شركة أفليت للتجارة الإلكترونية",
    companyLogo: "/logo.png",
    companyPhone: "01XXXXXXXXX",
    companyEmail: "<EMAIL>",
    companyAddress: "القاهرة، مصر",
  });

  // Load company settings from localStorage
  useEffect(() => {
    const savedSettings = localStorage.getItem("companySettings");
    if (savedSettings) {
      try {
        setCompanySettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error("Error parsing saved company settings:", error);
      }
    }
  }, []);

  const handlePrintInvoice = () => {
    if (!order) return;

    try {
      // إعداد بيانات الفاتورة
      const invoiceData: InvoiceData = {
        orderNumber: order.orderNumber,
        customerName: order.customerName,
        customerPhone: order.customerPhone,
        customerAddress: order.customerAddress,
        notes: order.notes,
        items: order.items.map(item => ({
          id: item.id,
          name: item.productName,
          quantity: item.quantity,
          price: item.price,
          total: item.total
        })),
        subtotal: order.items.reduce((sum, item) => sum + item.total, 0),
        shippingFee: order.shippingFee,
        total: order.items.reduce((sum, item) => sum + item.total, 0) + order.shippingFee,
        date: new Date(order.createdAt).toLocaleDateString('en-GB', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        })
      };

      // إعداد بيانات الشركة
      const companyData: CompanySettings = {
        companyName: companySettings.companyName,
        companyLogo: companySettings.companyLogo,
        companyPhone: companySettings.companyPhone,
        companyEmail: companySettings.companyEmail,
        companyAddress: companySettings.companyAddress
      };

      // طباعة الفاتورة
      printInvoice(invoiceData, companyData);
      toast.success("جاري طباعة الفاتورة");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "حدث خطأ أثناء طباعة الفاتورة");
    }
  };

  const handleStatusChange = async (newStatus: OrderStatus) => {
    if (!order || !id) return;

    try {
      // محاكاة تأخير API
      await new Promise(resolve => setTimeout(resolve, 800));

      // تحديث حالة الطلب - هذه الدالة تقوم بتحديث القسم أيضًا
      const updatedOrder = updateOrderStatus(id, newStatus);

      // تحديث البيانات المحلية
      if (updatedOrder) {
        setOrder(updatedOrder);
      }

      // تحديث جميع الاستعلامات ذات الصلة
      invalidateAllOrderQueries(queryClient);

      toast.success(`تم تغيير حالة الطلب إلى ${translateStatus(newStatus)}`);

      // إظهار رسائل إعلامية فقط بدون توجيه تلقائي
      if (newStatus === "confirmed") {
        toast.info("تم تحويل الطلب إلى قسم المخازن للتجهيز");
      } else if (newStatus === "shipped") {
        toast.info("تم تحويل الطلب إلى قسم الشحن");
      } else if (newStatus === "processing") {
        toast.info("تم بدء تجهيز الطلب");
      }
    } catch (err) {
      toast.error("حدث خطأ أثناء تحديث حالة الطلب");
    }
  };

  // وظيفة حذف الطلب وتحويله للأرشيف
  const handleDeleteOrder = async () => {
    if (!order || !id) return;

    if (!deleteReason.trim()) {
      toast.error("يرجى إدخال سبب الحذف");
      return;
    }

    try {
      setIsDeleting(true);

      // محاكاة تأخير API
      await new Promise(resolve => setTimeout(resolve, 800));

      // تحديث الطلب: تغيير الحالة إلى "ملغي" وتحويله إلى قسم الأرشيف
      const updatedOrder = {
        ...order,
        status: "cancelled" as OrderStatus,
        section: "archive" as OrderSection,
        notes: order.notes
          ? `${order.notes}\n\nسبب الحذف: ${deleteReason}`
          : `سبب الحذف: ${deleteReason}`,
        updatedAt: new Date().toISOString()
      };

      // تحديث الطلب في التخزين المحلي
      updateOrder(updatedOrder);

      // تحديث جميع الاستعلامات ذات الصلة
      invalidateAllOrderQueries(queryClient);

      toast.success("تم حذف الطلب وتحويله إلى الأرشيف");

      // إغلاق نافذة الحوار والعودة إلى الصفحة السابقة
      setIsDeleteDialogOpen(false);
      navigate(-1);
    } catch (err) {
      toast.error("حدث خطأ أثناء حذف الطلب");
    } finally {
      setIsDeleting(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-[70vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4">جاري تحميل بيانات الطلب...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !order) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-[70vh]">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto" />
            <h2 className="text-xl font-bold mt-4">{error || "حدث خطأ غير متوقع"}</h2>
            <Button
              className="mt-4 bg-blue-600 hover:bg-blue-700"
              onClick={() => navigate(-1)}
            >
              العودة للخلف
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* رأس الصفحة */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              طلب #{order.orderNumber}
            </h1>
            <div className="flex items-center gap-2 mt-2">
              <Badge variant="outline" className={getStatusColor(order.status)}>
                {translateStatus(order.status)}
              </Badge>
              <span className="text-sm text-gray-500">
                تم الإنشاء في {new Date(order.createdAt).toLocaleDateString("ar-EG")}
              </span>
            </div>
          </div>
          <div className="flex gap-2">
            {/* زر تعديل الطلب - يظهر دائمًا */}
            <Button
              variant="default"
              className="gap-2"
              onClick={() => navigate(`/edit-order/${order.id}`)}
            >
              <Edit className="h-4 w-4" />
              تعديل الطلب
            </Button>

            {/* زر حذف الطلب - يظهر فقط للطلبات في قسم المخزن */}
            {order.section === "warehouse" && (
              <Button
                variant="destructive"
                className="gap-2"
                onClick={() => setIsDeleteDialogOpen(true)}
              >
                <Trash2 className="h-4 w-4" />
                حذف الطلب
              </Button>
            )}
            <Button
              variant="outline"
              className="gap-2"
              onClick={handlePrintInvoice}
            >
              <Printer className="h-4 w-4" />
              طباعة الفاتورة
            </Button>

          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* معلومات العميل */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>معلومات العميل</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium text-gray-500">الاسم</h3>
                  <p className="font-medium">{order.customerName}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-500">رقم الهاتف</h3>
                  <p className="font-medium">{order.customerPhone}</p>
                  {order.customerPhone2 && (
                    <p className="font-medium">{order.customerPhone2}</p>
                  )}
                </div>
                <div className="md:col-span-2">
                  <h3 className="font-medium text-gray-500">العنوان</h3>
                  <p className="font-medium">{order.customerAddress}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-500">المحافظة</h3>
                  <p className="font-medium">{order.province}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-500">المدينة</h3>
                  <p className="font-medium">{order.city}</p>
                </div>
                {order.customerNotes && (
                  <div className="md:col-span-2">
                    <h3 className="font-medium text-gray-500">ملاحظات العميل</h3>
                    <p className="font-medium">{order.customerNotes}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* معلومات المسوق */}
          <Card>
            <CardHeader>
              <CardTitle>معلومات المسوق</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium text-gray-500">الاسم</h3>
                  <p className="font-medium">{order.marketerName}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-500">معرف المسوق</h3>
                  <p className="font-medium">{order.marketerId}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-500">العمولة</h3>
                  <p className="font-medium text-blue-600">{order.commission.toFixed(2)} ج.م</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* تفاصيل الطلب */}
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>تفاصيل الطلب</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.items.map((item) => (
                  <div key={item.id} className="flex items-center gap-4 border-b pb-4">
                    <div
                      className="w-16 h-16 bg-gray-100 rounded-md overflow-hidden flex-shrink-0 cursor-pointer relative group"
                      onClick={() => {
                        setSelectedImage(item.image || "/placeholder.svg");
                        setIsImageModalOpen(true);
                      }}
                    >
                      <img
                        src={item.image || "/placeholder.svg"}
                        alt={item.productName}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                        <ZoomIn className="h-5 w-5 text-white" />
                      </div>
                    </div>
                    <div className="flex-grow">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{item.productName}</h3>
                        {/* إضافة علامة صح للمنتجات المسلمة في حالة التسليم الجزئي */}
                        {order.status === "partially_delivered" && (
                          item.delivered ? (
                            <Badge className="bg-green-100 text-green-800 flex items-center gap-1">
                              <Check className="h-3 w-3" />
                              تم التسليم
                            </Badge>
                          ) : (
                            <Badge className="bg-red-100 text-red-800 flex items-center gap-1">
                              <X className="h-3 w-3" />
                              لم يتم التسليم
                            </Badge>
                          )
                        )}
                      </div>
                      <div className="flex flex-wrap gap-x-4 text-sm text-gray-500">
                        {item.color && <span>اللون: {item.color}</span>}
                        {item.size && <span>المقاس: {item.size}</span>}
                        <span>السعر: {item.price} ج.م</span>
                      </div>
                      {/* إضافة سبب الرفض إذا كان موجودًا */}
                      {order.status === "partially_delivered" && !item.delivered && item.rejectionReason && (
                        <div className="text-sm text-red-600 mt-1">
                          سبب الرفض: {item.rejectionReason}
                        </div>
                      )}
                    </div>
                    <div className="text-center">
                      <span className="inline-flex items-center justify-center w-8 h-8 text-sm font-medium border rounded-full">
                        {item.quantity}
                      </span>
                      <p className="text-sm font-medium mt-1">{item.total} ج.م</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* ملخص الطلب */}
          <Card>
            <CardHeader>
              <CardTitle>ملخص الطلب</CardTitle>
            </CardHeader>
            <CardContent>
              <OrderSummary
                productTotal={order.items.reduce((sum, item) => sum + item.total, 0)}
                shippingFee={order.shippingFee}
                discount={order.discount || 0}
                commission={order.commission}
                province={order.province}
                items={order.items}
              />

              <Separator className="my-4" />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>طريقة الدفع:</span>
                  <span className="font-medium">{translatePaymentMethod(order.paymentMethod)}</span>
                </div>
                <div className="flex justify-between">
                  <span>حالة الدفع:</span>
                  <Badge variant={order.paymentStatus === "paid" ? "success" : "destructive"}>
                    {translatePaymentStatus(order.paymentStatus)}
                  </Badge>
                </div>
              </div>

              {order.notes && (
                <>
                  <Separator className="my-4" />
                  <div>
                    <h3 className="font-medium text-gray-500 mb-1">ملاحظات إضافية</h3>
                    <p className="text-sm">{order.notes}</p>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </div>

        {/* إجراءات الطلب */}
        <Card>
          <CardHeader>
            <CardTitle>إجراءات الطلب</CardTitle>
          </CardHeader>
          <CardContent>
            <OrderActions
              order={order}
              onStatusChange={handleStatusChange}
            />

            {/* زر الإغلاق */}
            <div className="flex justify-center mt-6">
              <Button
                onClick={() => {
                  // العودة إلى الصفحة السابقة بدلاً من التوجيه بناءً على قسم الطلب
                  navigate(-1);
                }}
                className="w-full sm:w-auto text-base py-6"
                variant="default"
                style={{ backgroundColor: '#1e40af', color: 'white' }}
              >
                <XCircle className="ml-3 h-5 w-5" />
                إغلاق
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Image Modal */}
      <ImageModal
        isOpen={isImageModalOpen}
        onClose={() => setIsImageModalOpen(false)}
        imageUrl={selectedImage || "/placeholder.svg"}
        title="صورة المنتج"
      />

      {/* Printable Invoice - Hidden until print */}
      {order && (
        <PrintableInvoice
          order={order}
          companyName="شركة أفليت للتجارة الإلكترونية"
          companyLogo="/logo.png"
        />
      )}

      {/* نافذة تأكيد حذف الطلب */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="text-xl text-red-600">تأكيد حذف الطلب</DialogTitle>
            <DialogDescription>
              أنت على وشك حذف الطلب #{order?.orderNumber} وتحويله إلى الأرشيف. هذا الإجراء لا يمكن التراجع عنه.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4 space-y-4">
            <div className="bg-amber-50 border border-amber-200 rounded-md p-4">
              <div className="flex items-start">
                <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 ml-2" />
                <div>
                  <h4 className="font-medium text-amber-800">تنبيه هام</h4>
                  <p className="text-sm text-amber-700 mt-1">
                    سيتم تغيير حالة الطلب إلى "ملغي" وتحويله إلى قسم أرشيف الطلبات.
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="delete-reason" className="text-base">سبب الحذف <span className="text-red-500">*</span></Label>
              <Textarea
                id="delete-reason"
                value={deleteReason}
                onChange={(e) => setDeleteReason(e.target.value)}
                placeholder="يرجى كتابة سبب حذف الطلب..."
                className="min-h-[100px]"
              />
              {!deleteReason.trim() && (
                <p className="text-sm text-red-500">سبب الحذف مطلوب</p>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isDeleting}
            >
              إلغاء
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteOrder}
              disabled={isDeleting || !deleteReason.trim()}
              className="gap-2"
            >
              {isDeleting ? (
                <>
                  <span className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full ml-1"></span>
                  جاري الحذف...
                </>
              ) : (
                <>
                  <Archive className="h-4 w-4" />
                  تأكيد الحذف والأرشفة
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
};

export default OrderDetailsPage;
